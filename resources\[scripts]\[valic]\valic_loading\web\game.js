const progressBar = document.getElementById('progressBar');
const progressPercentage = document.getElementById('progressPercentage');
const loadingText = document.getElementById('loadingText');

const loadingStates = {
    0: "Inicializace...",
    5: "Spouštím systémy...",
    15: "Načítám mapu...",
    25: "Mapa načtena...",
    35: "Připojuji session...",
    45: "Načítám prohlížeč...",
    55: "Načítám detaily...",
    65: "Dokončuji session...",
    75: "Připravuji svět...",
    85: "Svět připraven...",
    95: "Dokončuji...",
    100: "Vítejte v Diverse RP!"
};

function updateProgress(percentage, customText = null) {
    progressBar.style.width = `${percentage}%`;
    progressPercentage.textContent = `${Math.round(percentage)}%`;

    // Použij custom text pokud je poskytnut, jinak použij přednastavené stavy
    if (customText) {
        loadingText.textContent = customText;
    } else {
        let currentText = loadingStates[0];
        for (const perc in loadingStates) {
            if (percentage >= perc) {
                currentText = loadingStates[perc];
            }
        }
        loadingText.textContent = currentText;
    }
}

// Hlavní event listener pro FiveM loading progress
window.addEventListener('message', function(e) {
    if (e.data.eventName === 'loadProgress') {
        const percentage = e.data.loadFraction * 100;
        const customText = e.data.loadingText || null;
        updateProgress(percentage, customText);

        // Pokud je loading dokončen (100%), připrav se na skrytí
        if (percentage >= 100) {
            setTimeout(() => {
                fadeOutLoadingScreen();
            }, 1500); // Počkej 1.5 sekundy před fade out
        }
    }
});

// Funkce pro plynulé skrytí loading screenu
function fadeOutLoadingScreen() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 1s ease-out';
    
    // Po dokončení fade out animace skryj loading screen
    setTimeout(() => {
        document.body.style.display = 'none';
        // Pošli signál FiveM, že je loading screen připraven k zavření
        try {
            fetch('https://valic_loading/loadingComplete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            }).catch(error => {
                console.log('Loading complete callback failed:', error);
            });
        } catch (error) {
            console.log('Failed to send loading complete signal:', error);
        }
    }, 1000);
}

if (typeof GetParentResourceName === 'undefined') {
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 5;
        if (progress > 100) {
            progress = 100;
            clearInterval(interval);
        }
        updateProgress(progress);
    }, 200);
}