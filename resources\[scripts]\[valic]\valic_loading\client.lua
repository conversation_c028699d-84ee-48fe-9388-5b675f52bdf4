local isLoadingScreenActive = true
local loadingProgress = 0

-- <PERSON>ce pro aktualizaci loading progress
local function updateLoadingProgress(progress, text)
    if isLoadingScreenActive then
        SendLoadingScreenMessage(json.encode({
            eventName = 'loadProgress',
            loadFraction = progress / 100,
            loadingText = text
        }))
    end
end

-- HTTP callback handler pro loading screen
RegisterNUICallback('loadingComplete', function(data, cb)
    if isLoadingScreenActive then
        print("^2[valic_loading] Loading screen fade out completed, shutting down^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
    cb('ok')
end)

-- Hlavní loading loop
Citizen.CreateThread(function()
    while isLoadingScreenActive do
        -- Získej aktuální loading progress z FiveM
        local gameProgress = GetLoadingScreenLoadFraction()
        
        if gameProgress then
            local progressPercent = math.floor(gameProgress * 100)
            
            -- Aktualizuj pouze pokud se progress změnil
            if progressPercent ~= loadingProgress then
                loadingProgress = progressPercent
                updateLoadingProgress(progressPercent)
            end
            
            -- Pokud je loading dokončen
            if progressPercent >= 100 then
                Citizen.Wait(2000) -- Počkej 2 sekundy před zavřením
                ShutdownLoadingScreen()
                isLoadingScreenActive = false
                break
            end
        end
        
        Citizen.Wait(100) -- Kontroluj každých 100ms
    end
end)

-- Event handlery pro custom loading stavy
RegisterNetEvent('valic_loading:updateProgress')
AddEventHandler('valic_loading:updateProgress', function(progress, text)
    updateLoadingProgress(progress, text)
end)

RegisterNetEvent('valic_loading:closeLoadingScreen')
AddEventHandler('valic_loading:closeLoadingScreen', function()
    if isLoadingScreenActive then
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

-- Automatické zavření loading screenu po připojení
AddEventHandler('playerSpawned', function()
    if isLoadingScreenActive then
        Citizen.Wait(1000)
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

-- Backup timer pro případ, že se loading screen nezavře automaticky
Citizen.CreateThread(function()
    Citizen.Wait(60000) -- 60 sekund timeout
    if isLoadingScreenActive then
        print("^3[valic_loading] Loading screen timeout - force closing^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)