[       437] [FiveM_DumpServ]                57368/ DumpServer is active and waiting.
[       437] [         FiveM]             MainThrd/ --- BEGIN LOGGING AT Sun Jul  6 07:33:19 2025 ---
[       531] [         FiveM]             MainThrd/ hello from "C:\Users\<USER>\AppData\Local\FiveM\FiveM.exe" 
[       531] [         FiveM]             MainThrd/ Got ros:launcher process - pid 20136
[       609] [FiveM_ROSLaunc]             MainThrd/ hello from "C:\Users\<USER>\AppData\Local\FiveM\FiveM.app\data\cache\subprocess\FiveM_ROSLauncher" ros:launcher --parent_pid=58024 "C:\Program Files\Rockstar Games\Launcher\Launcher.exe" -noRecogniser
[       672] [FiveM_ROSLaunc]             MainThrd/ launcher! "C:\Users\<USER>\AppData\Local\FiveM\FiveM.app\data\cache\subprocess\FiveM_ROSLauncher" ros:launcher --parent_pid=58024 "C:\Program Files\Rockstar Games\Launcher\Launcher.exe" -noRecogniser
[       734] [FiveM_ROSLaunc]             MainThrd/ Replacing function table list entry 0x7ff660fce000 with 0x7ff660fce000
[       906] [FiveM_ROSLaunc]                40240/ Got ROS service - pid 39564
[       937] [FiveM_ROSServi]             MainThrd/ hello from "C:\Users\<USER>\AppData\Local\FiveM\FiveM.app\data\cache\subprocess\FiveM_ROSService" ros:service
[      1016] [FiveM_ROSServi]             MainThrd/ service! "C:\Users\<USER>\AppData\Local\FiveM\FiveM.app\data\cache\subprocess\FiveM_ROSService" ros:service
[      1016] [FiveM_ROSServi]             MainThrd/ Replacing function table list entry 0x7ff66ec82000 with 0x7ff66ec82000
[      1094] [FiveM_ROSServi]             MainThrd/ SetServiceStatus: 4
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: SetLauncherVersions -> {"launcher":"1.0.53.576","socialclub":"2.0.9.0"}
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: SetSettings -> {"machine":{"global":{"defaultInstallationPath":"","defaultLanguage":"","lastSysInfoSendDate":1751746097,"maxPath":77,"maxPathW":77},"titles":{"lanoire":{"adapter":"Default","ambientOcclusion":"YES","anisotropicFiltering":1,"antialiasing":0,"environmentMap":"Quality","facialQuality":"Quality","fullScreen":"NO","interfaceSize":"Normal","keyboard":"US","language":"English","lightingQuality":"Quality","lodQuality":"Quality","renderer":"DirectX 11","resolution":"1280x720x60","shadowQuality":"Quality","textureQuality":"Quality","threading":"Multi"},"lanoirevr":{"adapter":"Default","ambientOcclusion":"NO","anisotropicFiltering":1,"antialiasing":1,"environmentMap":"Quality","facialQuality":"Quality","fullScreen":"NO","interfaceSize":"Normal","keyboard":"US","language":"English","languageSet":"NO","lightingQuality":"Quality","lodQuality":"Quality","renderer":"DirectX 11","resolution":"1280x720x60","resolutionMultiplier":1.0,"shadowQuality":"Balanced","ssaoQuality":"Balanced","textureQuality":"Quality","threading":"Multi","videoHwDecoding":"NO"}}},"rockstarUser":{"global":{"notifications":{"all":true,"download":true,"friends":true,"game":true,"launcher":true,"scui":true}},"titles":{"bully":{"autoUpdate":true},"gta3":{"autoUpdate":true},"gta5":{"autoUpdate":true},"gtasa":{"autoUpdate":true},"gtavc":{"autoUpdate":true},"lanoire":{"autoUpdate":true,"commandLine":""},"lanoirevr":{"commandLine":""},"mp3":{"autoUpdate":true},"rdr2":{"autoUpdate":true},"rdr2_rdo":{"autoUpdate":true},"rdr2_sp":{"autoUpdate":true},"rdr2_sp_rgl":{"autoUpdate":true}}},"windowsUser":{"externalLaunch":{"requested":{"language":""}},"global":{"allowGamePad":true,"backgroundDownloadThrottle":1048576,"backgroundDownloadThrottleEnabled":false,"hibernateOnGameLaunch":false,"inGameDownloadEnabled":false,"inGameDownloadThrottle":1048576,"inGameDownloadThrottleEnabled":false,"language":"en-US","mainWindow":{"fixedres":1,"h":0,"maximize":false,"w":0,"x":0,"y":0},"minimizeOnClose":false,"preloadThrottle":524288,"preloadThrottleEnabled":true,"preloadingEnabled":true,"shownMinimizingToTrayMessage":false,"startWithWindows":false}}}
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: SetFullMode -> {}
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: NotifyDiskSpace -> {"C:":{"freeBytes":125170417664,"totalBytes":499447259136}}
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto III","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta3"}
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtavc) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: Vice City","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtavc"}
[      1531] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp_rgl) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp_rgl"}
[      1547] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp"}
[      1547] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (mp3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Max Payne 3: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"mp3"}
[      1547] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (bully) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Bully: Scholarship Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pl-PL","ru-RU","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"bully"}
[      1547] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtasa) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: San Andreas","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtasa"}
[      1547] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2) -> {"branches":[{"branchName":12,"friendlyName":"default","isDefault":true}],"currentBranch":12,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"Red Dead Redemption 2","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2"}
[      1547] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_rdo) -> {"branches":[{"branchName":14,"friendlyName":"default","isDefault":true}],"currentBranch":14,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_rdo"}
[      1547] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoire) -> {"batchEnd":true,"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","ru-RU"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoire"}
[      1844] [FiveM_ROSLaunc]                57936/ SC JS message: SetSettings -> {"machine":{"global":{"defaultInstallationPath":"","defaultLanguage":"","lastSysInfoSendDate":1751746097,"maxPath":0,"maxPathW":0},"titles":{"lanoire":{"adapter":"Default","ambientOcclusion":"YES","anisotropicFiltering":1,"antialiasing":0,"environmentMap":"Quality","facialQuality":"Quality","fullScreen":"NO","interfaceSize":"Normal","keyboard":"US","language":"English","lightingQuality":"Quality","lodQuality":"Quality","renderer":"DirectX 11","resolution":"1280x720x60","shadowQuality":"Quality","textureQuality":"Quality","threading":"Multi"},"lanoirevr":{"adapter":"Default","ambientOcclusion":"NO","anisotropicFiltering":1,"antialiasing":1,"environmentMap":"Quality","facialQuality":"Quality","fullScreen":"NO","interfaceSize":"Normal","keyboard":"US","language":"English","languageSet":"NO","lightingQuality":"Quality","lodQuality":"Quality","renderer":"DirectX 11","resolution":"1280x720x60","resolutionMultiplier":1.0,"shadowQuality":"Balanced","ssaoQuality":"Balanced","textureQuality":"Quality","threading":"Multi","videoHwDecoding":"NO"}}},"rockstarUser":{"global":{"notifications":{"all":true,"download":true,"friends":true,"game":true,"launcher":true,"scui":true}},"titles":{"bully":{"autoUpdate":true},"gta3":{"autoUpdate":true},"gta5":{"autoUpdate":true},"gtasa":{"autoUpdate":true},"gtavc":{"autoUpdate":true},"lanoire":{"autoUpdate":true,"commandLine":""},"lanoirevr":{"commandLine":""},"mp3":{"autoUpdate":true},"rdr2":{"autoUpdate":true},"rdr2_rdo":{"autoUpdate":true},"rdr2_sp":{"autoUpdate":true},"rdr2_sp_rgl":{"autoUpdate":true}}},"windowsUser":{"externalLaunch":{"requested":{"language":""}},"global":{"allowGamePad":true,"backgroundDownloadThrottle":1048576,"backgroundDownloadThrottleEnabled":false,"hibernateOnGameLaunch":false,"inGameDownloadEnabled":false,"inGameDownloadThrottle":1048576,"inGameDownloadThrottleEnabled":false,"language":"en-US","mainWindow":{"fixedres":1,"h":0,"maximize":false,"w":0,"x":0,"y":0},"minimizeOnClose":false,"preloadThrottle":524288,"preloadThrottleEnabled":true,"preloadingEnabled":true,"shownMinimizingToTrayMessage":false,"startWithWindows":false}}}
[      1859] [FiveM_ROSLaunc]                57936/ SC JS message: SetGameLibraries -> {"libraries":[{"location":"C:\\Program Files\\Rockstar Games\\Games","spaceBytes":125170397184},{"location":"C:\\Program Files\\Rockstar Games","spaceBytes":125170397184}]}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto III","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta3"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:28:00+00:00","preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoirevr) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: The VR Case Files","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"neverInstallable","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoirevr"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtavc) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: Vice City","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtavc"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp_rgl) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp_rgl"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (mp3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Max Payne 3: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"mp3"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (bully) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Bully: Scholarship Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pl-PL","ru-RU","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"bully"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtasa) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: San Andreas","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtasa"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2) -> {"branches":[{"branchName":12,"friendlyName":"default","isDefault":true}],"currentBranch":12,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"Red Dead Redemption 2","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_rdo) -> {"branches":[{"branchName":14,"friendlyName":"default","isDefault":true}],"currentBranch":14,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_rdo"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoire) -> {"batchEnd":true,"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","ru-RU"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoire"}
[      2062] [FiveM_ROSLaunc]                57936/ SC JS message: SignInComplete -> {}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto III","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta3"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:28:00+00:00","preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoirevr) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: The VR Case Files","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"neverInstallable","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoirevr"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtavc) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: Vice City","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtavc"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp_rgl) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp_rgl"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (mp3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Max Payne 3: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"mp3"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (bully) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Bully: Scholarship Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pl-PL","ru-RU","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"bully"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtasa) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: San Andreas","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtasa"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2) -> {"branches":[{"branchName":12,"friendlyName":"default","isDefault":true}],"currentBranch":12,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"Red Dead Redemption 2","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_rdo) -> {"branches":[{"branchName":14,"friendlyName":"default","isDefault":true}],"currentBranch":14,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_rdo"}
[      3125] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoire) -> {"batchEnd":true,"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","ru-RU"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoire"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto III","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta3"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:28:00+00:00","preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoirevr) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: The VR Case Files","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"neverInstallable","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoirevr"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtavc) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: Vice City","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtavc"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp_rgl) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp_rgl"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (mp3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Max Payne 3: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"mp3"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (bully) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Bully: Scholarship Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pl-PL","ru-RU","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"bully"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtasa) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: San Andreas","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtasa"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2) -> {"branches":[{"branchName":12,"friendlyName":"default","isDefault":true}],"currentBranch":12,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"Red Dead Redemption 2","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_rdo) -> {"branches":[{"branchName":14,"friendlyName":"default","isDefault":true}],"currentBranch":14,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_rdo"}
[      3516] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoire) -> {"batchEnd":true,"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","ru-RU"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoire"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto III","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta3"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:28:00+00:00","preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoirevr) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: The VR Case Files","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"neverInstallable","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoirevr"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtavc) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: Vice City","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtavc"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp_rgl) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp_rgl"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_sp) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_sp"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (mp3) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Max Payne 3: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"mp3"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (bully) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Bully: Scholarship Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pl-PL","ru-RU","ja-JP"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"bully"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gtasa) -> {"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"Grand Theft Auto: San Andreas","languages":["en-US","fr-FR","de-DE","it-IT","es-ES"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gtasa"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2) -> {"branches":[{"branchName":12,"friendlyName":"default","isDefault":true}],"currentBranch":12,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"Red Dead Redemption 2","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (rdr2_rdo) -> {"branches":[{"branchName":14,"friendlyName":"default","isDefault":true}],"currentBranch":14,"currentBranchFriendlyName":"default","currentVersion":"","externalInstall":[],"friendlyName":"","languages":[],"parentApp":"rdr2","shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"prerelease","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"rdr2_rdo"}
[      3906] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (lanoire) -> {"batchEnd":true,"branches":[],"currentBranch":-1,"currentBranchFriendlyName":"","currentVersion":"","externalInstall":[],"friendlyName":"L.A. Noire: Complete Edition","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","ru-RU"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":false,"install":false,"preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":0,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"lanoire"}
[      3937] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"batchEnd":true,"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:28:00+00:00","preparing":false,"queuePosition":0,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"updateQueued","updateTotal":0},"titleName":"gta5"}
[      3953] [FiveM_ROSLaunc]                57936/ SC JS message: SetUpdateProgress -> {"bytesPerSecond":0,"bytesProgress":0,"bytesTotal":56066032,"progress":"0.00","status":"","titleName":"gta5"}
[      3953] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"batchEnd":true,"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:28:00+00:00","preparing":false,"queuePosition":0,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"starting","updateTotal":0},"titleName":"gta5"}
[      4156] [FiveM_ROSLaunc]                57936/ SC JS message: SetUpdateProgress -> {"bytesPerSecond":0,"bytesProgress":56066032,"bytesTotal":56066032,"progress":"100.00","status":"","titleName":"gta5"}
[      4156] [FiveM_ROSLaunc]                57936/ SC JS message: SetUpdateProgress -> {"bytesPerSecond":0,"bytesProgress":0,"bytesTotal":0,"progress":"0.00","status":"","titleName":"gta5"}
[      4156] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"batchEnd":true,"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:28:00+00:00","preparing":false,"queuePosition":0,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      4172] [FiveM_ROSLaunc]                57936/ SC JS message: SetGameLaunchState -> {"launchState":"prelaunch","message":"","titleName":"gta5"}
[      4172] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"batchEnd":true,"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:33:23+00:00","preparing":false,"queuePosition":0,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      4484] [FiveM_ROSLaunc]                57936/ SC JS message: SetTitleInfo (gta5) -> {"batchEnd":true,"branches":[{"branchName":13,"friendlyName":"default","isDefault":true}],"currentBranch":13,"currentBranchFriendlyName":"default","currentVersion":"1.0.3258.0","externalInstall":[],"friendlyName":"Grand Theft Auto V","languages":["en-US","fr-FR","de-DE","it-IT","es-ES","pt-BR","pl-PL","ru-RU","ko-KR","zh-CHT","ja-JP","es-MX","zh-CHS"],"parentApp":null,"shortcuts":{"desktop":false,"startMenu":false},"status":{"entitlement":true,"install":true,"installLocation":"C:\\Program Files\\Rockstar Games\\Games\\Grand Theft Auto V","lastUsed":"2025-07-06T09:33:23+00:00","preparing":false,"queuePosition":-1,"releaseState":"available","totalBytes":56066032,"updateBytes":0,"updateProgress":0,"updateState":"notUpdating","updateTotal":0},"titleName":"gta5"}
[      4797] [FiveM_GTAProce]             MainThrd/ CitizenFX Steam child starting - command line: "C:\Users\<USER>\AppData\Local\FiveM\FiveM.exe" 
[      4797] [FiveM_GTAProce]             MainThrd/ hello from "C:\Users\<USER>\AppData\Local\FiveM\FiveM.exe" 
[      5219] [FiveM_ROSLaunc]                57936/ SC JS message: SetGameLaunchState -> {"launchState":"running","message":"","titleName":"gta5"}
[      6547] [FiveM_SteamChi]             MainThrd/ CitizenFX Steam child starting - command line: "C:\Users\<USER>\AppData\Local\FiveM\FiveM.app\data\cache\subprocess\FiveM_SteamChild.exe" -steamparent:54988
[      6547] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent.
[      6562] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent: Steam's running.
[      6641] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent: Initializing presence.
[      6641] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent: Attempting to run processes.
[      8922] [FiveM_GTAProce]             MainThrd/ Replacing function table list entry 0x7ff6b313f000 with 0x7ff6b313f000
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component fx
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component rage:allocator:five
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component net:base
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component net:tcp-server
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component net:packet
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component vfs:core
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:resources:core
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component rage:scripting:five
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component scripting:gta
[      8922] [FiveM_GTAProce]             MainThrd/ pre-gameload component conhost:v2
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component http-client
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:core
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:v8client
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component steam
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:resources:client
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component rage:device:five
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component pool-sizes-state
[      8937] [FiveM_GTAProce]             MainThrd/ pre-gameload component rage:nutsnbolts:five
[      8953] [FiveM_GTAProce]             MainThrd/ Skipping XAudio2 patches - XAudio 2.7 is present.
[      8953] [FiveM_GTAProce]             MainThrd/ pre-gameload component rage:input:five
[      8953] [FiveM_GTAProce]             MainThrd/ pre-gameload component rage:graphics:five
[      8953] [FiveM_GTAProce]             MainThrd/ Ignored graphics mod: D:\SteamLibrary\steamapps\common\Grand Theft Auto V\dxgi.dll - these should go in plugins/ now!
[      8953] [FiveM_GTAProce]             MainThrd/ pre-gameload component gta:core:five
[      9000] [FiveM_GTAProce]             MainThrd/ pre-gameload component gta:game:five
[      9000] [FiveM_GTAProce]             MainThrd/ pre-gameload component gta:mission-cleanup:five
[      9000] [FiveM_GTAProce]             MainThrd/ pre-gameload component rage:formats:x
[      9000] [FiveM_GTAProce]             MainThrd/ pre-gameload component gta:streaming:five
[      9016] [FiveM_GTAProce]             MainThrd/ >16 GiB of system RAM, increasing streaming allocator size to 2 GiB
[      9031] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:level-loader:five
[      9047] [FiveM_GTAProce]             MainThrd/ pre-gameload component profiles
[      9047] [FiveM_GTAProce]             MainThrd/ pre-gameload component net
[      9047] [FiveM_GTAProce]             MainThrd/ pre-gameload component scrbind:base
[      9047] [FiveM_GTAProce]             MainThrd/ pre-gameload component voip:mumble
[      9047] [FiveM_GTAProce]             MainThrd/ pre-gameload component gta:net:five
[      9062] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:resources:gta
[      9062] [FiveM_GTAProce]             MainThrd/ pre-gameload component font-renderer
[      9062] [FiveM_GTAProce]             MainThrd/ [FontRenderer] Initializing DirectWrite.
[      9062] [FiveM_GTAProce]             MainThrd/ pre-gameload component net:http-server
[      9062] [FiveM_GTAProce]             MainThrd/ pre-gameload component legitimacy
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component nui:core
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component nui:resources
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component devcon
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component devtools:five
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component discord
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:legacy-net:resources
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component debug:net
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:game:ipc
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:game:main
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:v8-v12.4
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component asi:five
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component scripthookv
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component lovely-script
[      9078] [FiveM_GTAProce]             MainThrd/ pre-gameload component extra-natives:five
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component vfs:impl:rage
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component glue
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component loading-screens:five
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:lua
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component handling-loader:five
[      9125] [FiveM_GTAProce] [Mumble] Audio Input/ MumbleAudioInput::InitializeAudioDevice: Initialized audio capture device.
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component nui:gsclient
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:mono
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:devtools
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component nui:profiles
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component tool:formats
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:resources:metadata:lua
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:mod-loader:five
[      9125] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:playernames:five
[      9141] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:lua54
[      9141] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:v8node
[      9141] [FiveM_GTAProce]             MainThrd/ pre-gameload component citizen:scripting:mono-v2
[      9141] [FiveM_GTAProce]             MainThrd/ pre-gameload component tool:vehrec
[      9141] [FiveM_GTAProce]             MainThrd/ pre-gameload component adhesive
[      9594] [FiveM_GTAProce]             MainThrd/ pre-gameload component fxdk:main
[     11562] [FiveM_GTAProce]               Render/ Creating backbuffer.
[     11562] [FiveM_GTAProce]               Render/ Done creating backbuffer.
[     16422] [FiveM_GTAProce]        CrBrowserMain/ OnConnectionProgress: Requesting server variables...
[     16516] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: Requesting authentication ticket...
[     16562] [FiveM_GTAProce]        UV loop: tcp0/ OnConnectionProgress: Handshaking with server...
[     16625] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: monitor: Deferring connection...
[     16625] [FiveM_GTAProce]  UV loop: httpClient/ qbx_core: Deferring connection...
[     16625] [FiveM_GTAProce]  UV loop: httpClient/ 
[     16656] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: monitor: 
[     16656] [FiveM_GTAProce]  UV loop: httpClient/ [txAdmin] Checking banlist/whitelist... (0/5)
[     16656] [FiveM_GTAProce]  UV loop: httpClient/ qbx_core: Deferring connection...
[     16656] [FiveM_GTAProce]  UV loop: httpClient/ 
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: monitor: 
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ [txAdmin] Checking banlist/whitelist... (1/5)
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ qbx_core: Deferring connection...
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ 
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: monitor: 
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ [txAdmin] Checking banlist/whitelist... (1/5)
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ qbx_core: Hello stepan_valic. We are checking if you are banned.
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ 
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: monitor: 
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ [txAdmin] Checking banlist/whitelist... (1/5)
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ qbx_core: Welcome stepan_valic to Diverse RP.
[     16703] [FiveM_GTAProce]  UV loop: httpClient/ 
[     16750] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: qbx_core: Welcome stepan_valic to Diverse RP.
[     16750] [FiveM_GTAProce]  UV loop: httpClient/ 
[     16750] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: Requesting server endpoints...
[     16797] [FiveM_GTAProce]  UV loop: httpClient/ OnConnectionProgress: Requesting server permissions...
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patch2023_02CRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patch2023_01_G9ECCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patch2023_01CRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay28G9ECNGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay28NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay27G9ECNGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay27NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDayG9ECNGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay26NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay25NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay24NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay22NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay23NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay21NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay20NGCRC:/content.xml
[     17031] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY19NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY18NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY17NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY16NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY15NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY13NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY14NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY12NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY11NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY10NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchday9NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_PATCHDAY8NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay7NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay6NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay5NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay4NGCRC:/content.xml
[     17047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay3NGCRC:/content.xml
[     17062] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay2bNGCRC:/content.xml
[     17062] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay2NGCRC:/content.xml
[     17062] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_patchDay1NGCRC:/content.xml
[     17062] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpPatchesNGCRC:/content.xml
[     17219] [FiveM_GTAProce]        CrBrowserMain/ OnConnectionProgress: Requesting server feature policy...
[     17641] [FiveM_GTAProce]                32676/ OnConnectionProgress: Downloading content
[     17672] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading content manifest...
[     17719] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading content manifest (42.32 kB)
[     17766] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading content manifest (217.43 kB)
[     17812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Loading content manifest...
[     17906] [FiveM_GTAProce]             MainThrd/ NullInitializeGraphics
[     17937] [FiveM_GTAProce]             MainThrd/ ^2Opening \\.\pipe\GTAVLauncher_Pipe, waiting for launcher to load...
[     17937] [FiveM_GTAProce]             MainThrd/ ^2Launcher gave all-clear - waiting for pipe.
[     17937] [FiveM_GTAProce]             MainThrd/ ^2Launcher is fine, continuing to initialize!
[     17953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running INIT_CORE init functions
[     17953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 1 (55 total)
[     17953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking InitSystem INIT_CORE init (1 out of 55)
[     17953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CShaderLib INIT_CORE init (2 out of 55)
[     17984] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking PostFX INIT_CORE init (3 out of 55)
[     18000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGtaAnimManager INIT_CORE init (4 out of 55)
[     18000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwClipSetManager INIT_CORE init (5 out of 55)
[     18031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking 0xa0f39fb6 INIT_CORE init (6 out of 55)
[     18031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwExpressionSetManager INIT_CORE init (7 out of 55)
[     18031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwFacialClipSetGroupManager INIT_CORE init (8 out of 55)
[     18031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking audNorthAudioEngine INIT_CORE init (9 out of 55)
[     18141] [FiveM_GTAProce]  UV loop: httpClient/ Required resources: monitor mapmanager chat spawnmanager sessionmanager hardcap baseevents ox_lib oxmysql qbx_core okokChatV2 ox_target ox_doorlock ox_inventory ox_fuel qbx_adminmenu qbx_binoculars qbx_busjob qbx_carwash qbx_cityhall qbx_divegear qbx_diving qbx_drugs qbx_fireworks qbx_garbagejob qbx_houserobbery MugShotBase64 qbx_idcard qbx_lapraces qbx_management qbx_medical qbx_newsjob qbx_properties qbx_radialmenu qbx_recyclejob qbx_scoreboard qbx_seatbelt qbx_smallresources qbx_streetraces qbx_taxijob qbx_towjob qbx_truckerjob qbx_vehiclesales qbx_vineyard bob74_ipl illenium-appearance informational mana_audio mhacking safecracker screencapture screenshot-basic ultra-voltlab vehiclehandler pma-voice mm_radio pillbox roads lation_selling DVRP-2.0-handling Diverse-2.0-cars Diverse-EMS Rmod_BallerSTD Rmod_Gauntlet_Hellfire cfx-pbbjv-jakers cfx-pbgjv-jakers cfx-pvsjv-jakers cfx-pvtjv-jakers compacts coupes emergency expolalamo jester3c_eng motorcycles muscle npolchar offroad rhinesed rt3000varis sedans sent5pd service sports sportsclassic super suvs RageUI Renewed-Lib Renewed-Businesses Renewed-Weathersync RiP-Crosshair bl_vehiclemenu cd_dispatch dvrp_utility envi-bridge envi-population fox_radioanim jraxion_weaponrecoil kq_animsuggest lation_247robbery lation_laundering lation_meth lation_weed lvc minigame okokBanking okokBilling okokGarage okokMulticharacter okokNotify okokPhone_props okokPhone okokSpawnSelector okokTextUI okokVehicleShop oulsen_map rahe-hackingdevice rahe-boosting rcore_prison rcore_prison_assets rcore_radiocar rcore_tuning rpemotes-reborn valic_hud valic_loading valic_odtahovkar wasabi_bridge wasabi_ambulance wasabi_carlock wasabi_crutch wasabi_evidence wasabi_police xsound TheMightCafe dnxnewcalafiaroads dnxnewsenoraroad 
[     18141] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying monitor (0 of 136 - 1.32/12.53 MiB)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCheat INIT_CORE init (10 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CClock INIT_CORE init (11 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCullZones INIT_CORE init (12 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CExpensiveProcessDistributer INIT_CORE init (13 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameLogic INIT_CORE init (14 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking 0xaa19234e INIT_CORE init (15 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CutSceneManagerWrapper INIT_CORE init (16 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CAnimSceneManager INIT_CORE init (17 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPortalTracker INIT_CORE init (18 out of 55)
[     18172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNetwork INIT_CORE init (19 out of 55)
[     18187] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying monitor (0 of 136 - 5.38/12.53 MiB)
[     18234] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying monitor (0 of 136 - 11.47/12.53 MiB)
[     18281] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying ox_lib (7 of 136 - 3.04/6.57 MiB)
[     18297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CObjectPopulationNY INIT_CORE init (20 out of 55)
[     18297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking COcclusion INIT_CORE init (21 out of 55)
[     18297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPlantMgr INIT_CORE init (22 out of 55)
[     18328] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying ox_doorlock (12 of 136 - 0.23/0.68 MiB)
[     18375] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying ox_inventory (13 of 136 - 5.38/16.26 MiB)
[     18437] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying ox_inventory (13 of 136 - 11.50/16.26 MiB)
[     18484] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying qbx_carwash (18 of 136 - 0.03/0.03 MiB)
[     18531] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying qbx_idcard (27 of 136 - 0.55/0.60 MiB)
[     18578] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying illenium-appearance (45 of 136 - 0.58/0.98 MiB)
[     18625] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying ultra-voltlab (52 of 136 - 3.26/4.49 MiB)
[     18672] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying DVRP-2.0-handling (59 of 136 - 0.35/6.87 MiB)
[     18719] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CShaderHairSort INIT_CORE init (23 out of 55)
[     18719] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNetworkTelemetry INIT_CORE init (24 out of 55)
[     18719] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPopulationStreamingWrapper INIT_CORE init (25 out of 55)
[     18719] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying DVRP-2.0-handling (59 of 136 - 6.02/6.87 MiB)
[     18719] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRenderer INIT_CORE init (26 out of 55)
[     18766] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying compacts (68 of 136 - 1.67/25.42 MiB)
[     18812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying compacts (68 of 136 - 7.71/25.42 MiB)
[     18859] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying compacts (68 of 136 - 13.85/25.42 MiB)
[     18906] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying compacts (68 of 136 - 17.90/25.42 MiB)
[     18953] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying compacts (68 of 136 - 23.78/25.42 MiB)
[     19000] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying coupes (69 of 136 - 4.12/34.86 MiB)
[     19078] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CReplayMgr INIT_CORE init (27 out of 55)
[     19078] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying coupes (69 of 136 - 14.18/34.86 MiB)
[     19078] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking VideoRecording INIT_CORE init (28 out of 55)
[     19078] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking VideoPlayback INIT_CORE init (29 out of 55)
[     19078] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking VideoPlaybackThumbnailManager INIT_CORE init (30 out of 55)
[     19078] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking 0x8930d2f7 INIT_CORE init (31 out of 55)
[     19078] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStatsMgr INIT_CORE init (32 out of 55)
[     19125] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying coupes (69 of 136 - 19.89/34.86 MiB)
[     19172] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying coupes (69 of 136 - 22.88/34.86 MiB)
[     19219] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying coupes (69 of 136 - 28.61/34.86 MiB)
[     19234] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CFrontendStatsMgr INIT_CORE init (33 out of 55)
[     19234] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CControllerLabelMgr INIT_CORE init (34 out of 55)
[     19234] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStuntJumpManager INIT_CORE init (35 out of 55)
[     19234] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTheScripts INIT_CORE init (36 out of 55)
[     19266] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying coupes (69 of 136 - 34.62/34.86 MiB)
[     19312] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 5.66/49.02 MiB)
[     19359] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 11.79/49.02 MiB)
[     19422] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 17.86/49.02 MiB)
[     19469] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 23.84/49.02 MiB)
[     19516] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 29.61/49.02 MiB)
[     19562] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 35.62/49.02 MiB)
[     19609] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 41.73/49.02 MiB)
[     19672] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying emergency (70 of 136 - 47.82/49.02 MiB)
[     19719] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 0.48/60.05 MiB)
[     19766] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 6.45/60.05 MiB)
[     19812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 12.37/60.05 MiB)
[     19859] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 18.35/60.05 MiB)
[     19906] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 22.30/60.05 MiB)
[     19969] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 26.59/60.05 MiB)
[     19969] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptAreas INIT_CORE init (37 out of 55)
[     19969] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptDebug INIT_CORE init (38 out of 55)
[     19969] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptHud INIT_CORE init (39 out of 55)
[     19984] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptPedAIBlips INIT_CORE init (40 out of 55)
[     19984] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTrain INIT_CORE init (41 out of 55)
[     19984] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CWaypointRecording INIT_CORE init (42 out of 55)
[     19984] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehicleRecordingMgr INIT_CORE init (43 out of 55)
[     19984] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTaskClassInfoManager INIT_CORE init (44 out of 55)
[     19984] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTuningManager INIT_CORE init (45 out of 55)
[     20000] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 30.23/60.05 MiB)
[     20016] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CAppDataMgr INIT_CORE init (46 out of 55)
[     20016] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNetRespawnMgr INIT_CORE init (47 out of 55)
[     20016] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CAgitatedManager INIT_CORE init (48 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScenarioActionManager INIT_CORE init (49 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehiclePopulation INIT_CORE init (50 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPostScan INIT_CORE init (51 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CExtraMetadataMgr INIT_CORE init (52 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking BackgroundScripts INIT_CORE init (53 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CIplCullBox INIT_CORE init (54 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking perfClearingHouse INIT_CORE init (55 out of 55)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 2 (11 total)
[     20031] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScene INIT_CORE init (1 out of 11)
[     20047] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 36.24/60.05 MiB)
[     20094] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 42.18/60.05 MiB)
[     20156] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 48.22/60.05 MiB)
[     20203] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying motorcycles (73 of 136 - 54.29/60.05 MiB)
[     20250] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 0.02/80.59 MiB)
[     20297] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 5.91/80.59 MiB)
[     20344] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 11.92/80.59 MiB)
[     20406] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 18.03/80.59 MiB)
[     20422] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking MeshBlendManager INIT_CORE init (2 out of 11)
[     20422] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVisualEffects INIT_CORE init (3 out of 11)
[     20453] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 23.91/80.59 MiB)
[     20500] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 29.95/80.59 MiB)
[     20547] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 36.02/80.59 MiB)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking ViewportSystemInit INIT_CORE init (4 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPhoneMgr INIT_CORE init (5 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPhotoManager INIT_CORE init (6 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CLODLightManager INIT_CORE init (7 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CLODLights INIT_CORE init (8 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking AmbientLights INIT_CORE init (9 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGps INIT_CORE init (10 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPedAILodManager INIT_CORE init (11 out of 11)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 3 (20 total)
[     20562] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPauseMenu INIT_CORE init (1 out of 20)
[     20594] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 41.98/80.59 MiB)
[     20656] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 47.87/80.59 MiB)
[     20703] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 53.74/80.59 MiB)
[     20750] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 59.71/80.59 MiB)
[     20797] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 65.68/80.59 MiB)
[     20844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA menuAud:/x64/audio/dlcTuner_sounds.dat.
[     20844] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying muscle (74 of 136 - 71.65/80.59 MiB)
[     20875] [FiveM_GTAProce]             MainThrd/ done loading menuAud:/x64/audio/dlcTuner_sounds.dat in data file mounter 0000000141a42838.
[     20875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK menuaud:/x64/audio/sfx/dlc_Tuner_Music.
[     20875] [FiveM_GTAProce]             MainThrd/ done loading menuaud:/x64/audio/sfx/dlc_Tuner_Music in data file mounter 0000000141a43eb0.
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking cStoreScreenMgr INIT_CORE init (2 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVideoEditorUI INIT_CORE init (3 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking WatermarkRenderer INIT_CORE init (4 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMousePointer INIT_CORE init (5 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTextInputBox INIT_CORE init (6 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking 0x602ee6e2 INIT_CORE init (7 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMiniMap INIT_CORE init (8 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNewHud INIT_CORE init (9 out of 20)
[     20875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking 0x4bfbfa2c INIT_CORE init (10 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking WarningScreen INIT_CORE init (11 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CBusySpinner INIT_CORE init (12 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking camManager INIT_CORE init (13 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CDLCScript INIT_CORE init (14 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameStreamMgr INIT_CORE init (15 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking 0x4d167300 INIT_CORE init (16 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwMetaDataStore INIT_CORE init (17 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CReplayCoordinator INIT_CORE init (18 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVideoEditorInterface INIT_CORE init (19 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking UI3DDrawManager INIT_CORE init (20 out of 20)
[     20891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Done running INIT_CORE init functions!
[     20891] [FiveM_GTAProce]             MainThrd/ OnConnectionProgress: Verifying muscle (74 of 136 - 77.34/80.59 MiB)
[     20969] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying npolchar (75 of 136 - 4.45/10.37 MiB)
[     21031] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying offroad (76 of 136 - 1.95/54.81 MiB)
[     21078] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying offroad (76 of 136 - 7.94/54.81 MiB)
[     21156] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying offroad (76 of 136 - 15.88/54.81 MiB)
[     21219] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying offroad (76 of 136 - 23.98/54.81 MiB)
[     21281] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying offroad (76 of 136 - 32.19/54.81 MiB)
[     21344] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying offroad (76 of 136 - 40.38/54.81 MiB)
[     21422] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying offroad (76 of 136 - 48.51/54.81 MiB)
[     21484] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 0.98/62.94 MiB)
[     21547] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 9.09/62.94 MiB)
[     21609] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 17.28/62.94 MiB)
[     21687] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 25.46/62.94 MiB)
[     21750] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 33.72/62.94 MiB)
[     21812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 41.88/62.94 MiB)
[     21891] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 50.08/62.94 MiB)
[     21953] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sedans (79 of 136 - 58.33/62.94 MiB)
[     22016] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying service (81 of 136 - 2.93/7.26 MiB)
[     22078] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 3.60/111.01 MiB)
[     22156] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 11.52/111.01 MiB)
[     22219] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 19.55/111.01 MiB)
[     22281] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 27.71/111.01 MiB)
[     22344] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 35.88/111.01 MiB)
[     22422] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 44.05/111.01 MiB)
[     22484] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 52.23/111.01 MiB)
[     22531] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 58.40/111.01 MiB)
[     22594] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 66.57/111.01 MiB)
[     22672] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 74.78/111.01 MiB)
[     22734] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 82.98/111.01 MiB)
[     22797] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 91.20/111.01 MiB)
[     22875] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 99.40/111.01 MiB)
[     22937] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sports (82 of 136 - 107.66/111.01 MiB)
[     23000] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sportsclassic (83 of 136 - 4.55/39.62 MiB)
[     23062] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sportsclassic (83 of 136 - 12.73/39.62 MiB)
[     23141] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sportsclassic (83 of 136 - 20.62/39.62 MiB)
[     23203] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sportsclassic (83 of 136 - 28.80/39.62 MiB)
[     23266] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying sportsclassic (83 of 136 - 36.99/39.62 MiB)
[     23312] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying super (84 of 136 - 3.23/52.43 MiB)
[     23375] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying super (84 of 136 - 9.47/52.43 MiB)
[     23437] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying super (84 of 136 - 17.52/52.43 MiB)
[     23500] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying super (84 of 136 - 25.67/52.43 MiB)
[     23562] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying super (84 of 136 - 33.84/52.43 MiB)
[     23641] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying super (84 of 136 - 42.02/52.43 MiB)
[     23703] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying super (84 of 136 - 50.21/52.43 MiB)
[     23766] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 5.63/75.96 MiB)
[     23812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 11.77/75.96 MiB)
[     23891] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 19.76/75.96 MiB)
[     23953] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 27.80/75.96 MiB)
[     24016] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 35.81/75.96 MiB)
[     24078] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 44.00/75.96 MiB)
[     24156] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 52.09/75.96 MiB)
[     24219] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 60.27/75.96 MiB)
[     24281] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying suvs (85 of 136 - 68.47/75.96 MiB)
[     24359] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying Renewed-Lib (87 of 136 - 0.05/0.05 MiB)
[     24422] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying fox_radioanim (96 of 136 - 0.01/0.01 MiB)
[     24484] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying lation_weed (102 of 136 - 0.10/0.33 MiB)
[     24547] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 0.88/80.19 MiB)
[     24625] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 9.06/80.19 MiB)
[     24687] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 17.28/80.19 MiB)
[     24750] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 25.48/80.19 MiB)
[     24812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 33.15/80.19 MiB)
[     24891] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 41.33/80.19 MiB)
[     24953] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 49.55/80.19 MiB)
[     25016] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 57.75/80.19 MiB)
[     25078] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 65.99/80.19 MiB)
[     25156] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokPhone (111 of 136 - 73.85/80.19 MiB)
[     25219] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying okokSpawnSelector (112 of 136 - 1.62/2.16 MiB)
[     25281] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying rcore_prison (118 of 136 - 4.05/7.53 MiB)
[     25359] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying rpemotes-reborn (122 of 136 - 1.38/1.78 MiB)
[     25422] [FiveM_GTAProce]               Render/ OnConnectionProgress: Mounted valic_hud (124 of 136)
[     25484] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 0.00/53.51 MiB)
[     25547] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 0.27/53.51 MiB)
[     25625] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 0.97/53.51 MiB)
[     25687] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 2.84/53.51 MiB)
[     25750] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 4.94/53.51 MiB)
[     25812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 8.05/53.51 MiB)
[     25891] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 9.98/53.51 MiB)
[     25953] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 11.88/53.51 MiB)
[     26016] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 14.06/53.51 MiB)
[     26094] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 16.30/53.51 MiB)
[     26156] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 18.19/53.51 MiB)
[     26219] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 20.28/53.51 MiB)
[     26281] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 22.30/53.51 MiB)
[     26344] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 24.02/53.51 MiB)
[     26406] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 26.06/53.51 MiB)
[     26469] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 27.34/53.51 MiB)
[     26531] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 28.33/53.51 MiB)
[     26609] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 29.52/53.51 MiB)
[     26672] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 30.78/53.51 MiB)
[     26734] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 31.53/53.51 MiB)
[     26781] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 32.30/53.51 MiB)
[     26859] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 33.09/53.51 MiB)
[     26906] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 33.86/53.51 MiB)
[     26969] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 34.64/53.51 MiB)
[     27031] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 35.81/53.51 MiB)
[     27109] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 36.61/53.51 MiB)
[     27172] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 37.41/53.51 MiB)
[     27234] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 38.55/53.51 MiB)
[     27312] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 39.58/53.51 MiB)
[     27375] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 40.75/53.51 MiB)
[     27437] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 41.94/53.51 MiB)
[     27500] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 43.16/53.51 MiB)
[     27578] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 44.31/53.51 MiB)
[     27641] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 45.36/53.51 MiB)
[     27703] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 46.55/53.51 MiB)
[     27766] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 47.83/53.51 MiB)
[     27844] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 49.08/53.51 MiB)
[     27906] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 50.34/53.51 MiB)
[     27969] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 51.03/53.51 MiB)
[     28031] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 52.19/53.51 MiB)
[     28109] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 53.09/53.51 MiB)
[     28156] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading valic_loading (124 of 136 - 53.51/53.51 MiB)
[     28641] [FiveM_GTAProce]                57364/ ResourceCache::AddEntry: Saved cache:v1:db2d315b41844be59db463368ba1dee867619465 to the index cache.
[     28672] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying valic_loading (124 of 136 - 4.28/53.51 MiB)
[     28734] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying valic_loading (124 of 136 - 11.48/53.51 MiB)
[     28812] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying valic_loading (124 of 136 - 19.03/53.51 MiB)
[     28875] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying valic_loading (124 of 136 - 26.95/53.51 MiB)
[     28937] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying valic_loading (124 of 136 - 35.02/53.51 MiB)
[     29000] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying valic_loading (124 of 136 - 43.25/53.51 MiB)
[     29078] [FiveM_GTAProce]               Render/ OnConnectionProgress: Verifying valic_loading (124 of 136 - 51.48/53.51 MiB)
[     29422] [FiveM_GTAProce]               Render/ OnConnectionProgress: Mounted dnxnewsenoraroad (136 of 136)
[     29422] [FiveM_GTAProce]               Render/ OnConnectionProgress: Downloading completed
[     29469] [FiveM_GTAProce]               Render/ OnConnectionProgress: Fetching info from server...
[     29547] [FiveM_GTAProce]               Render/ OnConnectionProgress: Connecting to server...
[     29687] [FiveM_SteamChi]             MainThrd/ CitizenFX Steam child starting - command line: "C:\Users\<USER>\AppData\Local\FiveM\FiveM.app\data\cache\subprocess\FiveM_SteamChild.exe" -steamparent:54988
[     29687] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent.
[     29719] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent: Steam's running.
[     29734] [FiveM_GTAProce]               Render/ ^2Received connectOK: ServerID 1, SlotID 128, HostID 65535
[     29766] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent: Initializing presence.
[     29766] [FiveM_SteamChi]             MainThrd/ Initializing Steam parent: Attempting to run processes.
[     29797] [FiveM_GTAProce]                32676/ ^2Network connected, triggering initial game load...
[     29891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running INIT_BEFORE_MAP_LOADED init functions
[     29891] [FiveM_GTAProce]             MainThrd/ Loading default meta overrides (total: 0)
[     29891] [FiveM_GTAProce]             MainThrd/ Done loading default meta overrides!
[     29891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 1 (31 total)
[     29891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGtaAnimManager INIT_BEFORE_MAP_LOADED init (1 out of 31)
[     29891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameWorld INIT_BEFORE_MAP_LOADED init (2 out of 31)
[     29891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGarages INIT_BEFORE_MAP_LOADED init (3 out of 31)
[     29891] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CHandlingDataMgr INIT_BEFORE_MAP_LOADED init (4 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ Loading 254 handling entries from commoncrc:/data/handling.meta
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking INSTANCESTORE INIT_BEFORE_MAP_LOADED init (5 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMapAreas INIT_BEFORE_MAP_LOADED init (6 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwMapTypesStore INIT_BEFORE_MAP_LOADED init (7 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CModelInfo INIT_BEFORE_MAP_LOADED init (8 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPatrolRoutes INIT_BEFORE_MAP_LOADED init (9 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPopZones INIT_BEFORE_MAP_LOADED init (10 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMapZoneManager INIT_BEFORE_MAP_LOADED init (11 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPhysics INIT_BEFORE_MAP_LOADED init (12 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTrain INIT_BEFORE_MAP_LOADED init (13 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking Water INIT_BEFORE_MAP_LOADED init (14 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking COcclusion INIT_BEFORE_MAP_LOADED init (15 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwClipSetManager INIT_BEFORE_MAP_LOADED init (16 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwExpressionSetManager INIT_BEFORE_MAP_LOADED init (17 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwFacialClipSetGroupManager INIT_BEFORE_MAP_LOADED init (18 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTVPlaylistManager INIT_BEFORE_MAP_LOADED init (19 out of 31)
[     29922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CAmbientModelSetManager INIT_BEFORE_MAP_LOADED init (20 out of 31)
[     29937] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CLadderMetadataManager INIT_BEFORE_MAP_LOADED init (21 out of 31)
[     29937] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScenarioManager INIT_BEFORE_MAP_LOADED init (22 out of 31)
[     29953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CConditionalAnimManager INIT_BEFORE_MAP_LOADED init (23 out of 31)
[     29969] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehicleMetadataMgr INIT_BEFORE_MAP_LOADED init (24 out of 31)
[     29969] [FiveM_GTAProce]        CrBrowserMain/ cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation (https://cdn.tailwindcss.com/:64)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRandomEventManager INIT_BEFORE_MAP_LOADED init (25 out of 31)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCrimeInformationManager INIT_BEFORE_MAP_LOADED init (26 out of 31)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CWitnessInformationManager INIT_BEFORE_MAP_LOADED init (27 out of 31)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPortal INIT_BEFORE_MAP_LOADED init (28 out of 31)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking LightEntityMgr INIT_BEFORE_MAP_LOADED init (29 out of 31)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking AnimBlackboard INIT_BEFORE_MAP_LOADED init (30 out of 31)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPathServer::InitBeforeMapLoaded INIT_BEFORE_MAP_LOADED init (31 out of 31)
[     30000] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Done running INIT_BEFORE_MAP_LOADED init functions!
[     30203] [FiveM_GTAProce]             MainThrd/ LoadScreenFuncs::OnEnd: Instrumented function 0x140e01168 (2) took 113msec
[     30484] [FiveM_GTAProce]             MainThrd/ LoadScreenFuncs::OnEnd: Instrumented function 0x14090f420 (53) took 283msec
[     46141] [FiveM_GTAProce]             MainThrd/ LoadScreenFuncs::OnEnd: Instrumented function 0x1407fafb4 (56) took 15657msec
[     46297] [FiveM_GTAProce]             MainThrd/ LoadScreenFuncs::OnEnd: Instrumented function 0x140939cec (24) took 80msec
[     46391] [FiveM_GTAProce]             MainThrd/ LoadScreenFuncs::OnEnd: Instrumented function 0x14093bb9c (33) took 94msec
[     46453] [FiveM_GTAProce]             MainThrd/ LoadScreenFuncs::OnEnd: Instrumented function 0x140e603a4 (38) took 54msec
[     46562] [FiveM_GTAProce]             MainThrd/ LoadScreenFuncs::OnEnd: Instrumented function 0x140917c80 (41) took 105msec
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running INIT_AFTER_MAP_LOADED init functions
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 1 (29 total)
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking audNorthAudioEngine INIT_AFTER_MAP_LOADED init (1 out of 29)
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CClock INIT_AFTER_MAP_LOADED init (2 out of 29)
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CEventDataManager INIT_AFTER_MAP_LOADED init (3 out of 29)
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CHandlingDataMgr INIT_AFTER_MAP_LOADED init (4 out of 29)
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMapAreas INIT_AFTER_MAP_LOADED init (5 out of 29)
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPed INIT_AFTER_MAP_LOADED init (6 out of 29)
[     46656] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPopCycle INIT_AFTER_MAP_LOADED init (7 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPopZones INIT_AFTER_MAP_LOADED init (8 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMapZoneManager INIT_AFTER_MAP_LOADED init (9 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPopulationStreamingWrapper INIT_AFTER_MAP_LOADED init (10 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStatsMgr INIT_AFTER_MAP_LOADED init (11 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStreamingRequestList INIT_AFTER_MAP_LOADED init (12 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTask INIT_AFTER_MAP_LOADED init (13 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTrain INIT_AFTER_MAP_LOADED init (14 out of 29)
[     46672] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehicleModelInfo INIT_AFTER_MAP_LOADED init (15 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehiclePopulation INIT_AFTER_MAP_LOADED init (16 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking Water INIT_AFTER_MAP_LOADED init (17 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameWorldHeightMap INIT_AFTER_MAP_LOADED init (18 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameWorldWaterHeight INIT_AFTER_MAP_LOADED init (19 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CAmbientAnimationManager INIT_AFTER_MAP_LOADED init (20 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CAmbientAudioManager INIT_AFTER_MAP_LOADED init (21 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNetwork INIT_AFTER_MAP_LOADED init (22 out of 29)
[     46797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwClipSetManager INIT_AFTER_MAP_LOADED init (23 out of 29)
[     51453] [FiveM_GTAProce]             MainThrd/ Warning: LoadObjectsNow took 4656 msec (invoked from 00007ff6b17a3119)!
[     51453] [FiveM_GTAProce]             MainThrd/ ---------------- DO FIX THE ABOVE ^
[     51453] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwAnimDirector INIT_AFTER_MAP_LOADED init (24 out of 29)
[     51578] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwExpressionSetManager INIT_AFTER_MAP_LOADED init (25 out of 29)
[     51578] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwFacialClipSetGroupManager INIT_AFTER_MAP_LOADED init (26 out of 29)
[     51578] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CThePopMultiplierAreas INIT_AFTER_MAP_LOADED init (27 out of 29)
[     51578] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking MeshBlendManager INIT_AFTER_MAP_LOADED init (28 out of 29)
[     51578] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CProceduralInfo INIT_AFTER_MAP_LOADED init (29 out of 29)
[     51594] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 2 (2 total)
[     51594] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVisualEffects INIT_AFTER_MAP_LOADED init (1 out of 2)
[     51828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPedModelInfo INIT_AFTER_MAP_LOADED init (2 out of 2)
[     51844] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Done running INIT_AFTER_MAP_LOADED init functions!
[     51844] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running INIT_SESSION init functions
[     52141] [FiveM_GTAProce]             MainThrd/ can't register compcache:/oulsen_map/minimap_sea_0_0.dds: no streaming module (does this file even belong in stream?)
[     52141] [FiveM_GTAProce]             MainThrd/ can't register compcache:/oulsen_map/minimap_sea_0_1.dds: no streaming module (does this file even belong in stream?)
[     52141] [FiveM_GTAProce]             MainThrd/ can't register compcache:/oulsen_map/minimap_sea_1_0.dds: no streaming module (does this file even belong in stream?)
[     52141] [FiveM_GTAProce]             MainThrd/ can't register compcache:/oulsen_map/minimap_sea_1_1.dds: no streaming module (does this file even belong in stream?)
[     52141] [FiveM_GTAProce]             MainThrd/ can't register compcache:/oulsen_map/minimap_sea_2_0.dds: no streaming module (does this file even belong in stream?)
[     52141] [FiveM_GTAProce]             MainThrd/ can't register compcache:/oulsen_map/minimap_sea_2_1.dds: no streaming module (does this file even belong in stream?)
[     52406] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 1 (5 total)
[     52406] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehicleRecordingMgr INIT_SESSION init (1 out of 5)
[     52406] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStreaming INIT_SESSION init (2 out of 5)
[     52406] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CFocusEntityMgr INIT_SESSION init (3 out of 5)
[     52406] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking audNorthAudioEngine INIT_SESSION init (4 out of 5)
[     53953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGtaAnimManager INIT_SESSION init (5 out of 5)
[     53953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 2 (84 total)
[     53953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CModelInfo::Init INIT_SESSION init (1 out of 84)
[     53953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CInstanceListAssetLoader::Init INIT_SESSION init (2 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTask INIT_SESSION init (3 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScenarioPointManagerInitSession INIT_SESSION init (4 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRenderPhaseCascadeShadowsInterface INIT_SESSION init (5 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwAnimDirector INIT_SESSION init (6 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwClipSetManager INIT_SESSION init (7 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwExpressionSetManager INIT_SESSION init (8 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwFacialClipSetGroupManager INIT_SESSION init (9 out of 84)
[     54797] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTaskRecover INIT_SESSION init (10 out of 84)
[     54812] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking camManager INIT_SESSION init (11 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CAssistedMovementRouteStore INIT_SESSION init (12 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CBuses INIT_SESSION init (13 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCheat INIT_SESSION init (14 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CControlMgr INIT_SESSION init (15 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameLogic INIT_SESSION init (16 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameWorld INIT_SESSION init (17 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGarages INIT_SESSION init (18 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGps INIT_SESSION init (19 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CJunctions INIT_SESSION init (20 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPathZoneManager INIT_SESSION init (21 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMessages INIT_SESSION init (22 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNetwork INIT_SESSION init (23 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CObjectPopulationNY INIT_SESSION init (24 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPathFind INIT_SESSION init (25 out of 84)
[     54828] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPed INIT_SESSION init (26 out of 84)
[     54859] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPedModelInfo INIT_SESSION init (27 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPedPopulation INIT_SESSION init (28 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPerformance INIT_SESSION init (29 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPedAILodManager INIT_SESSION init (30 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehicleAILodManager INIT_SESSION init (31 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPhoneMgr INIT_SESSION init (32 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPhotoManager INIT_SESSION init (33 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPhysics INIT_SESSION init (34 out of 84)
[     54875] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPickupManager INIT_SESSION init (35 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNetworkTelemetry INIT_SESSION init (36 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPopCycle INIT_SESSION init (37 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CProcObjectMan INIT_SESSION init (38 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CReplayMgr INIT_SESSION init (39 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRestart INIT_SESSION init (40 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScaleformMgr INIT_SESSION init (41 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CSlownessZonesManager INIT_SESSION init (42 out of 84)
[     54922] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStreamingRequestList INIT_SESSION init (43 out of 84)
[     54937] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStuntJumpManager INIT_SESSION init (44 out of 84)
[     54937] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CActionManager INIT_SESSION init (45 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRenderTargetMgr INIT_SESSION init (46 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTheScripts INIT_SESSION init (47 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptAreas INIT_SESSION init (48 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptCars INIT_SESSION init (49 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptPeds INIT_SESSION init (50 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptEntities INIT_SESSION init (51 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptDebug INIT_SESSION init (52 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptHud INIT_SESSION init (53 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptPedAIBlips INIT_SESSION init (54 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CStatsMgr INIT_SESSION init (55 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTrain INIT_SESSION init (56 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CUserDisplay INIT_SESSION init (57 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehiclePopulation INIT_SESSION init (58 out of 84)
[     54953] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVisualEffects INIT_SESSION init (59 out of 84)
[     55109] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CDispatchData INIT_SESSION init (60 out of 84)
[     55109] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRoadBlock INIT_SESSION init (61 out of 84)
[     55109] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CWeaponManager INIT_SESSION init (62 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScriptedGunTaskMetadataMgr INIT_SESSION init (63 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehicleCombatAvoidanceArea INIT_SESSION init (64 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCombatInfoMgr INIT_SESSION init (65 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCombatDirector INIT_SESSION init (66 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CVehicleChaseDirector INIT_SESSION init (67 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CBoatChaseDirector INIT_SESSION init (68 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTacticalAnalysis INIT_SESSION init (69 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCoverFinder INIT_SESSION init (70 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CWorldPoints INIT_SESSION init (71 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking fwTimer INIT_SESSION init (72 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking PostFX INIT_SESSION init (73 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRenderer INIT_SESSION init (74 out of 84)
[     55250] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking ViewportSystemInitLevel INIT_SESSION init (75 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGameSituation INIT_SESSION init (76 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPrioritizedClipSetRequestManager INIT_SESSION init (77 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPrioritizedClipSetStreamer INIT_SESSION init (78 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CSituationalClipSetStreamer INIT_SESSION init (79 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CClipDictionaryStoreInterface INIT_SESSION init (80 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CRiots INIT_SESSION init (81 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CCover INIT_SESSION init (82 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CExtraMetadataMgr::ClassInit INIT_SESSION init (83 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking BackgroundScripts INIT_SESSION init (84 out of 84)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Running functions of order 3 (18 total)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CText INIT_SESSION init (1 out of 18)
[     55266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMiniMap INIT_SESSION init (2 out of 18)
[     55281] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CNewHud INIT_SESSION init (3 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking 0x4bfbfa2c INIT_SESSION init (4 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CBusySpinner INIT_SESSION init (5 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CMultiplayerChat INIT_SESSION init (6 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CExplosionManager INIT_SESSION init (7 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPickupDataManager INIT_SESSION init (8 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPopulationStreaming INIT_SESSION init (9 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CDecoratorInterface INIT_SESSION init (10 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CScenarioManager INIT_SESSION init (11 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CGestureManager INIT_SESSION init (12 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CTexLod INIT_SESSION init (13 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPathServer::InitSession INIT_SESSION init (14 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CPauseMenu INIT_SESSION init (15 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CExtraContentWrapper INIT_SESSION init (16 out of 18)
[     55297] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPBeachCRC:/content.xml
[     55344] [FiveM_GTAProce]             MainThrd/ Loading 4 handling entries from dlcMPBeachCRC:/common/data/handling.meta
[     55375] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpChristmasCRC:/content.xml
[     55391] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPValentinesCRC:/content.xml
[     55594] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from dlcMPValentinesCRC:/common/data/handling.meta
[     55609] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPBusinessCRC:/content.xml
[     55844] [FiveM_GTAProce]             MainThrd/ Loading 4 handling entries from dlcMPBusinessCRC:/common/data/handling.meta
[     55891] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpBusiness2CRC:/content.xml
[     55969] [FiveM_GTAProce]             MainThrd/ Loading 4 handling entries from dlc_mpBusiness2CRC:/common/data/handling.meta
[     56016] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPHipsterCRC:/content.xml
[     56109] [FiveM_GTAProce]             MainThrd/ Loading 7 handling entries from dlcMPHipsterCRC:/common/data/handling.meta
[     56156] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpIndependenceCRC:/content.xml
[     56281] [FiveM_GTAProce]             MainThrd/ Duplicate Archetype 'p_w_ar_musket_chrg' (3E15FEA3), seen in 'Extra' and 'v_minigame.ytyp'
[     56281] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from dlc_mpIndependenceCRC:/common/data/handling.meta
[     56297] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPPilotCRC:/content.xml
[     56406] [FiveM_GTAProce]             MainThrd/ Loading 4 handling entries from dlcMpPilotCRC:/common/data/handling.meta
[     56422] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPLTSCRC:/content.xml
[     56562] [FiveM_GTAProce]             MainThrd/ Loading 3 handling entries from dlcMPLTSCRC:/common/data/handling.meta
[     56609] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpChristmas2CRC:/content.xml
[     56703] [FiveM_GTAProce]             MainThrd/ Loading 4 handling entries from dlc_mpChristmas2CRC:/common/data/handling.meta
[     56719] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPHeistCRC:/content.xml
[     57016] [FiveM_GTAProce]             MainThrd/ Loading 16 handling entries from dlcMPHeistCRC:/common/data/handling.meta
[     57125] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpLuxeCRC:/content.xml
[     57281] [FiveM_GTAProce]             MainThrd/ Loading 6 handling entries from dlc_mpLuxeCRC:/common/data/handling.meta
[     57312] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpLuxe2CRC:/content.xml
[     57391] [FiveM_GTAProce]             MainThrd/ Loading 6 handling entries from dlc_mpLuxe2CRC:/common/data/handling.meta
[     57422] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpReplayCRC:/content.xml
[     57469] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpLowriderCRC:/content.xml
[     57875] [FiveM_GTAProce]             MainThrd/ Loading 6 handling entries from dlc_mpLowriderCRC:/common/data/handling.meta
[     57922] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcMPHalloweenCRC:/content.xml
[     57969] [FiveM_GTAProce]             MainThrd/ Loading 2 handling entries from dlcMPHalloweenCRC:/common/data/handling.meta
[     57984] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpApartmentCRC:/content.xml
[     58344] [FiveM_GTAProce]             MainThrd/ Loading 18 handling entries from dlc_mpApartmentCRC:/common/data/handling.meta
[     58391] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpxmas_604490CRC:/content.xml
[     58453] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from dlc_mpxmas_604490CRC:/common/data/handling.meta
[     58469] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpJanuary2016CRC:/content.xml
[     58531] [FiveM_GTAProce]             MainThrd/ Loading 2 handling entries from dlc_mpJanuary2016CRC:/common/data/handling.meta
[     58562] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpValentines2CRC:/content.xml
[     58625] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from dlc_mpValentines2CRC:/common/data/handling.meta
[     58625] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpLowrider2CRC:/content.xml
[     58781] [FiveM_GTAProce]             MainThrd/ Loading 7 handling entries from dlc_mpLowrider2CRC:/common/data/handling.meta
[     58844] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpExecutiveCRC:/content.xml
[     59078] [FiveM_GTAProce]             MainThrd/ Loading 14 handling entries from dlc_mpExecutiveCRC:/common/data/handling.meta
[     59125] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpStuntCRC:/content.xml
[     59328] [FiveM_GTAProce]             MainThrd/ Loading 15 handling entries from dlc_mpStuntCRC:/common/data/handling.meta
[     59375] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpBikerCRC:/content.xml
[     59937] [FiveM_GTAProce]             MainThrd/ Loading 21 handling entries from dlc_mpBikerCRC:/common/data/handling.meta
[     60047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpImportExportCRC:/content.xml
[     60391] [FiveM_GTAProce]             MainThrd/ Loading 23 handling entries from dlc_mpImportExportCRC:/common/data/handling.meta
[     60484] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpSpecialRacesCRC:/content.xml
[     60641] [FiveM_GTAProce]             MainThrd/ Loading 4 handling entries from dlc_mpSpecialRacesCRC:/common/data/handling.meta
[     60672] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpGunRunningCRC:/content.xml
[     61125] [FiveM_GTAProce]             MainThrd/ Loading 18 handling entries from dlc_mpGunRunningCRC:/common/data/handling.meta
[     61344] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpAirRacesCRC:/content.xml
[     61453] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpSmugglerCRC:/content.xml
[     61859] [FiveM_GTAProce]             MainThrd/ Loading 19 handling entries from dlc_mpSmugglerCRC:/common/data/handling.meta
[     62016] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpChristmas2017CRC:/content.xml
[     62672] [FiveM_GTAProce]             MainThrd/ Loading 29 handling entries from dlc_mpChristmas2017CRC:/common/data/handling.meta
[     62906] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpAssaultCRC:/content.xml
[     63109] [FiveM_GTAProce]             MainThrd/ Loading 16 handling entries from dlc_mpassaultCRC:/common/data/handling.meta
[     63172] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpBattleCRC:/content.xml
[     63609] [FiveM_GTAProce]             MainThrd/ Loading 14 handling entries from dlc_mpBattleCRC:/common/data/handling.meta
[     63750] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpChristmas2018CRC:/content.xml
[     65203] [FiveM_GTAProce]             MainThrd/ Loading 33 handling entries from dlc_mpChristmas2018CRC:/common/data/handling.meta
[     65469] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpVinewoodCRC:/content.xml
[     66016] [FiveM_GTAProce]             MainThrd/ Loading 22 handling entries from dlc_mpVinewoodCRC:/common/data/handling.meta
[     66187] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpHeist3CRC:/content.xml
[     66859] [FiveM_GTAProce]             MainThrd/ Loading 20 handling entries from dlc_mpHeist3CRC:/common/data/handling.meta
[     67094] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpSumCRC:/content.xml
[     67344] [FiveM_GTAProce]             MainThrd/ Loading 15 handling entries from dlc_mpSumCRC:/common/data/handling.meta
[     67672] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpHeist4CRC:/content.xml
[     68328] [FiveM_GTAProce]             MainThrd/ Loading 20 handling entries from dlc_mpHeist4CRC:/common/data/handling.meta
[     68531] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpTunerCRC:/content.xml
[     69094] [FiveM_GTAProce]             MainThrd/ Loading 17 handling entries from dlc_mpTunerCRC:/common/data/handling.meta
[     69297] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpSecurityCRC:/content.xml
[     69906] [FiveM_GTAProce]             MainThrd/ Loading 16 handling entries from dlc_mpSecurityCRC:/common/data/handling.meta
[     70047] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_mpG9ECCRC:/content.xml
[     70109] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MPSUM2CRC:/content.xml
[     70516] [FiveM_GTAProce]             MainThrd/ Loading 18 handling entries from dlc_mpSum2CRC:/common/data/handling.meta
[     70672] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MPSUM2_G9ECCRC:/content.xml
[     70687] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MPCHRISTMAS3CRC:/content.xml
[     71203] [FiveM_GTAProce]             MainThrd/ Loading 13 handling entries from dlc_mpChristmas3CRC:/common/data/handling.meta
[     71391] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MPCHRISTMAS3_G9ECCRC:/content.xml
[     71391] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MP2023_01CRC:/content.xml
[     71859] [FiveM_GTAProce]             MainThrd/ Loading 14 handling entries from dlc_mp2023_01CRC:/common/data/handling.meta
[     72078] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MP2023_01_G9ECCRC:/content.xml
[     72078] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MP2023_02CRC:/content.xml
[     72625] [FiveM_GTAProce]             MainThrd/ Loading 23 handling entries from dlc_mp2023_02CRC:/common/data/handling.meta
[     72828] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlc_MP2023_02_G9ECCRC:/content.xml
[     72844] [FiveM_GTAProce]             MainThrd/ Loading content XML: dlcSPUpgradeCRC:/content.xml
[     73031] [FiveM_GTAProce]             MainThrd/ Loading 12 handling entries from dlcSPUpgrade:/common/data/handling.meta
[     78266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking CDLCScript INIT_SESSION init (17 out of 18)
[     78266] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Invoking audNorthAudioEngineDLC INIT_SESSION init (18 out of 18)
[     78297] [FiveM_GTAProce]             MainThrd/ Loading mounted data files (total: 672)
[     78297] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/DVRP-2.0-handling/baseGame/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 758 handling entries from resources:/DVRP-2.0-handling/baseGame/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/Diverse-2.0-cars/data/schlagenstr/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/Diverse-2.0-cars/data/schlagenstr/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/schlagenstr/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/Diverse-2.0-cars/data/sunrise/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/Diverse-2.0-cars/data/sunrise/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/sunrise/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/Diverse-EMS/data/ambuimp/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/Diverse-EMS/data/ambuimp/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/ambuimp/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/Diverse-EMS/data/emsscout/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/Diverse-EMS/data/emsscout/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/emsscout/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/Rmod_BallerSTD/data/rmod_baller8/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/Rmod_BallerSTD/data/rmod_baller8/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_BallerSTD/data/rmod_baller8/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_LAYOUTS_FILE resources:/Rmod_BallerSTD/data/rmod_baller8/vehiclelayouts.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_BallerSTD/data/rmod_baller8/vehiclelayouts.meta in data file mounter 0000000141b4b110.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/cfx-pbbjv-jakers/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/cfx-pbbjv-jakers/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbbjv-jakers/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/cfx-pbgjv-jakers/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/cfx-pbgjv-jakers/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbgjv-jakers/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/cfx-pvsjv-jakers/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/cfx-pvsjv-jakers/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvsjv-jakers/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/cfx-pvtjv-jakers/handling.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/cfx-pvtjv-jakers/handling.meta
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvtjv-jakers/handling.meta in data file mounter 0000000141b49720.
[     78359] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_LAYOUTS_FILE resources:/expolalamo/vehiclelayouts.meta.
[     78359] [FiveM_GTAProce]             MainThrd/ done loading resources:/expolalamo/vehiclelayouts.meta in data file mounter 0000000141b4b110.
[     78359] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/expolalamo/handling.meta.
[     78375] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/expolalamo/handling.meta
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/expolalamo/handling.meta in data file mounter 0000000141b49720.
[     78375] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/rhinesed/data/handling.meta.
[     78375] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/rhinesed/data/handling.meta
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/rhinesed/data/handling.meta in data file mounter 0000000141b49720.
[     78375] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/rt3000varis/data/handling.meta.
[     78375] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/rt3000varis/data/handling.meta
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/rt3000varis/data/handling.meta in data file mounter 0000000141b49720.
[     78375] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/sent5pd/data/handling.meta.
[     78375] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/sent5pd/data/handling.meta
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/sent5pd/data/handling.meta in data file mounter 0000000141b49720.
[     78375] [FiveM_GTAProce]             MainThrd/ loading HANDLING_FILE resources:/wasabi_crutch/chair_data/handling.meta.
[     78375] [FiveM_GTAProce]             MainThrd/ Loading 1 handling entries from resources:/wasabi_crutch/chair_data/handling.meta
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/wasabi_crutch/chair_data/handling.meta in data file mounter 0000000141b49720.
[     78375] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_ENTRY RELOAD_MAP_STORE.
[     78375] [FiveM_GTAProce]             MainThrd/ done loading RELOAD_MAP_STORE in data file mounter 00007ffd97b12af8.
[     78375] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/ox_doorlock/audio/dlc_oxdoorlock.
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/ox_doorlock/audio/dlc_oxdoorlock in data file mounter 0000000141a43eb0.
[     78375] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/ox_doorlock/audio/data/oxdoorlock_sounds.dat.
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/ox_doorlock/audio/data/oxdoorlock_sounds.dat in data file mounter 0000000141a42838.
[     78375] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/qbx_seatbelt/audiodirectory.
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/qbx_seatbelt/audiodirectory in data file mounter 0000000141a43eb0.
[     78375] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/qbx_seatbelt/data/seatbelt_sounds.dat.
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/qbx_seatbelt/data/seatbelt_sounds.dat in data file mounter 0000000141a42838.
[     78375] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/ultra-voltlab/audiodata/dlchei4_game.dat.
[     78375] [FiveM_GTAProce]             MainThrd/ failed loading resources:/ultra-voltlab/audiodata/dlchei4_game.dat in data file mounter 0000000141a42838.
[     78375] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/ultra-voltlab/audiodata/dlchei4_sounds.dat.
[     78375] [FiveM_GTAProce]             MainThrd/ failed loading resources:/ultra-voltlab/audiodata/dlchei4_sounds.dat in data file mounter 0000000141a42838.
[     78375] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/ultra-voltlab/dlc_hei4.
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/ultra-voltlab/dlc_hei4 in data file mounter 0000000141a43eb0.
[     78375] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/pillbox/stream/v_int_40.ytyp.
[     78375] [FiveM_GTAProce]             MainThrd/ done loading resources:/pillbox/stream/v_int_40.ytyp in data file mounter 00007ffd97b12c58.
[     78375] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/common/vehicles.meta.
[     78469] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/common/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78469] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mp2023_01/vehicles.meta.
[     78469] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mp2023_01/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78469] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mp2023_02/vehicles.meta.
[     78484] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mp2023_02/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78484] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpapartment/vehicles.meta.
[     78500] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpapartment/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78500] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpassault/vehicles.meta.
[     78500] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpassault/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78500] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbattle/vehicles.meta.
[     78500] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbattle/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78500] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbeach/vehicles.meta.
[     78500] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbeach/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78500] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbiker/vehicles.meta.
[     78516] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbiker/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78516] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbuisness/vehicles.meta.
[     78516] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbuisness/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78516] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbuisness2/vehicles.meta.
[     78516] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpbuisness2/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78516] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpchristmas2017/vehicles.meta.
[     78531] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpchristmas2017/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78531] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpchristmas2018/vehicles.meta.
[     78547] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpchristmas2018/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78547] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpchristmas3/vehicles.meta.
[     78547] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpchristmas3/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78547] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpexecutive/vehicles.meta.
[     78562] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpexecutive/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78562] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpgunrunning/vehicles.meta.
[     78562] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpgunrunning/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78562] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mphalloween/vehicles.meta.
[     78562] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mphalloween/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78562] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpheist/vehicles.meta.
[     78578] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpheist/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78578] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpheist3/vehicles.meta.
[     78578] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpheist3/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78578] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpheist4/vehicles.meta.
[     78594] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpheist4/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78594] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mphipster/vehicles.meta.
[     78594] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mphipster/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78594] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpimportexport/vehicles.meta.
[     78609] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpimportexport/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78609] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpindependence/vehicles.meta.
[     78609] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpindependence/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78609] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpjanuary2016/vehicles.meta.
[     78609] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpjanuary2016/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78609] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mplowrider/vehicles.meta.
[     78609] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mplowrider/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78609] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mplowrider2/vehicles.meta.
[     78609] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mplowrider2/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78609] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mplts/vehicles.meta.
[     78609] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mplts/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78609] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpluxe/vehicles.meta.
[     78625] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpluxe/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78625] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpluxe2/vehicles.meta.
[     78625] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpluxe2/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78625] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mppilot/vehicles.meta.
[     78625] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mppilot/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78625] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsecurity/vehicles.meta.
[     78625] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsecurity/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78625] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsmuggler/vehicles.meta.
[     78641] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsmuggler/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78641] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpspecialraces/vehicles.meta.
[     78641] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpspecialraces/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78641] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpstunt/vehicles.meta.
[     78641] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpstunt/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78641] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsum/vehicles.meta.
[     78656] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsum/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78656] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsum2/vehicles.meta.
[     78656] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpsum2/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78656] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mptuner/vehicles.meta.
[     78672] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mptuner/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78672] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpvalentines/vehicles.meta.
[     78672] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpvalentines/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78672] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpvalentines2/vehicles.meta.
[     78672] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpvalentines2/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78672] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpvinewood/vehicles.meta.
[     78672] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpvinewood/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78672] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpxmas_604490/vehicles.meta.
[     78672] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/mpxmas_604490/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78672] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/DVRP-2.0-handling/baseGame/vehiclemeta/spupgrade/vehicles.meta.
[     78687] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/vehiclemeta/spupgrade/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78687] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/DVRP-2.0-handling/baseGame/carcols.meta.
[     78687] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/carcols.meta in data file mounter 0000000141a7aa28.
[     78687] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/DVRP-2.0-handling/baseGame/carvariations.meta.
[     78687] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/baseGame/carvariations.meta in data file mounter 0000000141a7aa10.
[     78687] [FiveM_GTAProce]             MainThrd/ loading WEAPONINFO_FILE resources:/DVRP-2.0-handling/data/weapons_fpmrsa.meta.
[     78687] [FiveM_GTAProce]             MainThrd/ done loading resources:/DVRP-2.0-handling/data/weapons_fpmrsa.meta in data file mounter 0000000141b500a8.
[     78687] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/vehicles.meta.
[     78687] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78687] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/Diverse-2.0-cars/data/schlagenstr/vehicles.meta.
[     78687] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/schlagenstr/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78687] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/Diverse-2.0-cars/data/sunrise/vehicles.meta.
[     78687] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/sunrise/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78687] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/carcols.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/carcols.meta in data file mounter 0000000141a7aa28.
[     78703] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/Diverse-2.0-cars/data/schlagenstr/carcols.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/schlagenstr/carcols.meta in data file mounter 0000000141a7aa28.
[     78703] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/Diverse-2.0-cars/data/sunrise/carcols.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/sunrise/carcols.meta in data file mounter 0000000141a7aa28.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/carvariations.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/rrelegyextreme--pro_slechtu+remiix/carvariations.meta in data file mounter 0000000141a7aa10.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/Diverse-2.0-cars/data/schlagenstr/carvariations.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/schlagenstr/carvariations.meta in data file mounter 0000000141a7aa10.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/Diverse-2.0-cars/data/sunrise/carvariations.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-2.0-cars/data/sunrise/carvariations.meta in data file mounter 0000000141a7aa10.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/Diverse-EMS/data/ambuimp/vehicles.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/ambuimp/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/Diverse-EMS/data/emsscout/vehicles.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/emsscout/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78703] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/Diverse-EMS/data/ambuimp/carcols.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/ambuimp/carcols.meta in data file mounter 0000000141a7aa28.
[     78703] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/Diverse-EMS/data/emsscout/carcols.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/emsscout/carcols.meta in data file mounter 0000000141a7aa28.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/Diverse-EMS/data/ambuimp/carvariations.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/ambuimp/carvariations.meta in data file mounter 0000000141a7aa10.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/Diverse-EMS/data/emsscout/carvariations.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Diverse-EMS/data/emsscout/carvariations.meta in data file mounter 0000000141a7aa10.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/Rmod_BallerSTD/data/rmod_baller8/vehicles.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_BallerSTD/data/rmod_baller8/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78703] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/Rmod_BallerSTD/data/rmod_baller8/carcols.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_BallerSTD/data/rmod_baller8/carcols.meta in data file mounter 0000000141a7aa28.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/Rmod_BallerSTD/data/rmod_baller8/carvariations.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_BallerSTD/data/rmod_baller8/carvariations.meta in data file mounter 0000000141a7aa10.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/vehicles.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78703] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/carcols.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/carcols.meta in data file mounter 0000000141a7aa28.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/carvariations.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/Rmod_Gauntlet_Hellfire/data/rmod_gauntlet4/carvariations.meta in data file mounter 0000000141a7aa10.
[     78703] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/cfx-pbbjv-jakers/vehicles.meta.
[     78703] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbbjv-jakers/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78703] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/cfx-pbbjv-jakers/carcols.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbbjv-jakers/carcols.meta in data file mounter 0000000141a7aa28.
[     78719] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/cfx-pbbjv-jakers/carvariations.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbbjv-jakers/carvariations.meta in data file mounter 0000000141a7aa10.
[     78719] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/cfx-pbgjv-jakers/vehicles.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbgjv-jakers/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78719] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/cfx-pbgjv-jakers/carcols.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbgjv-jakers/carcols.meta in data file mounter 0000000141a7aa28.
[     78719] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/cfx-pbgjv-jakers/carvariations.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pbgjv-jakers/carvariations.meta in data file mounter 0000000141a7aa10.
[     78719] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/cfx-pvsjv-jakers/vehicles.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvsjv-jakers/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78719] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/cfx-pvsjv-jakers/carcols.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvsjv-jakers/carcols.meta in data file mounter 0000000141a7aa28.
[     78719] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/cfx-pvsjv-jakers/carvariations.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvsjv-jakers/carvariations.meta in data file mounter 0000000141a7aa10.
[     78719] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/cfx-pvtjv-jakers/vehicles.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvtjv-jakers/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78719] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/cfx-pvtjv-jakers/carcols.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvtjv-jakers/carcols.meta in data file mounter 0000000141a7aa28.
[     78719] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/cfx-pvtjv-jakers/carvariations.meta.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/cfx-pvtjv-jakers/carvariations.meta in data file mounter 0000000141a7aa10.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/compacts/audioconfig/blista_game.dat.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/blista_game.dat in data file mounter 0000000141a42838.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/compacts/audioconfig/blista_sounds.dat.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/blista_sounds.dat in data file mounter 0000000141a42838.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/compacts/sfx/dlc_blista.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/sfx/dlc_blista in data file mounter 0000000141a43eb0.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SYNTHDATA resources:/compacts/audioconfig/dilettante_amp.dat.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/dilettante_amp.dat in data file mounter 0000000141a42838.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/compacts/audioconfig/dilettante_game.dat.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/dilettante_game.dat in data file mounter 0000000141a42838.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/compacts/audioconfig/dilettante_sounds.dat.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/dilettante_sounds.dat in data file mounter 0000000141a42838.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/compacts/sfx/dlc_dilettante.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/sfx/dlc_dilettante in data file mounter 0000000141a43eb0.
[     78719] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/compacts/audioconfig/issi2_game.dat.
[     78719] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/issi2_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/compacts/audioconfig/issi2_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/issi2_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/compacts/sfx/dlc_issi2.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/sfx/dlc_issi2 in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/compacts/audioconfig/panto_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/panto_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/compacts/audioconfig/panto_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/panto_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/compacts/sfx/dlc_panto.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/sfx/dlc_panto in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/compacts/audioconfig/prairie_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/prairie_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/compacts/audioconfig/prairie_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/prairie_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/compacts/sfx/dlc_prairie.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/sfx/dlc_prairie in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/compacts/audioconfig/rhapsody_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/rhapsody_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/compacts/audioconfig/rhapsody_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/audioconfig/rhapsody_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/compacts/sfx/dlc_rhapsody.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/compacts/sfx/dlc_rhapsody in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/cogcabrio_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/cogcabrio_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/cogcabrio_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/cogcabrio_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_cogcabrio.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_cogcabrio in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/exemplar_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/exemplar_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/exemplar_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/exemplar_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_exemplar.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_exemplar in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/f620_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/f620_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/f620_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/f620_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_f620.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_f620 in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/felon_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/felon_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/felon_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/felon_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_felon.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_felon in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/jackal_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/jackal_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/jackal_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/jackal_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_jackal.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_jackal in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/oracle_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/oracle_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/oracle_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/oracle_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_oracle.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_oracle in data file mounter 0000000141a43eb0.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/oracle2_game.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/oracle2_game.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/oracle2_sounds.dat.
[     78734] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/oracle2_sounds.dat in data file mounter 0000000141a42838.
[     78734] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_oracle2.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_oracle2 in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/sentinel_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/sentinel_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/sentinel_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/sentinel_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_sentinel.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_sentinel in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/windsor_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/windsor_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/windsor_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/windsor_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_windsor.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_windsor in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/coupes/audioconfig/zion_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/zion_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/coupes/audioconfig/zion_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/audioconfig/zion_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/coupes/sfx/dlc_zion.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/coupes/sfx/dlc_zion in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/policeb_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policeb_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/policeb_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policeb_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_policeb.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_policeb in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/sheriff_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/sheriff_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/sheriff_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/sheriff_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_sheriff.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_sheriff in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/police2_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/police2_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/police2_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/police2_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_police2.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_police2 in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/police3_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/police3_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/police3_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/police3_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_police3.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_police3 in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/fbi2_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/fbi2_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/fbi2_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/fbi2_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_fbi2.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_fbi2 in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/policet_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policet_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/policet_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policet_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_policet.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_policet in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/policeold1_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policeold1_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/policeold1_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policeold1_sounds.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_policeold1.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_policeold1 in data file mounter 0000000141a43eb0.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/policeold2_game.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policeold2_game.dat in data file mounter 0000000141a42838.
[     78750] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/policeold2_sounds.dat.
[     78750] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/policeold2_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_policeold2.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_policeold2 in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/riot_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/riot_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/riot_sounds.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/riot_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_riot.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_riot in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/firetruk_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/firetruk_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/firetruk_sounds.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/firetruk_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_firetruk.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_firetruk in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/ambulance_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/ambulance_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/ambulance_sounds.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/ambulance_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_ambulance.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_ambulance in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/emergency/audioconfig/pbus_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/pbus_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/emergency/audioconfig/pbus_sounds.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/audioconfig/pbus_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/emergency/sfx/dlc_pbus.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/emergency/sfx/dlc_pbus in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/expolalamo/vehicles.meta.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/expolalamo/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78766] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/expolalamo/carcols.meta.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/expolalamo/carcols.meta in data file mounter 0000000141a7aa28.
[     78766] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/expolalamo/carvariations.meta.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/expolalamo/carvariations.meta in data file mounter 0000000141a7aa10.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/jester3c_eng/audioconfig/jestermk4_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/jester3c_eng/audioconfig/jestermk4_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/jester3c_eng/audioconfig/jestermk4_sounds.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/jester3c_eng/audioconfig/jestermk4_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/jester3c_eng/sfx/dlc_jestermk4.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/jester3c_eng/sfx/dlc_jestermk4 in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/akuma_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/akuma_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/akuma_sounds.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/akuma_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_akuma.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_akuma in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/bagger_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/bagger_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/bagger_sounds.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/bagger_sounds.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_bagger.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_bagger in data file mounter 0000000141a43eb0.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/bati_game.dat.
[     78766] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/bati_game.dat in data file mounter 0000000141a42838.
[     78766] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/bati_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/bati_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_bati.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_bati in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/bati2_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/bati2_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/bati2_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/bati2_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_bati2.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_bati2 in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/carbonrs_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/carbonrs_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/carbonrs_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/carbonrs_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_carbonrs.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_carbonrs in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/daemon_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/daemon_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/daemon_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/daemon_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_daemon.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_daemon in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/double_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/double_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/double_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/double_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_double.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_double in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/faggio_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/faggio_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/faggio_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/faggio_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_faggio.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_faggio in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/hakuchou_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/hakuchou_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/hakuchou_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/hakuchou_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_hakuchou.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_hakuchou in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/hexer_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/hexer_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/hexer_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/hexer_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_hexer.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_hexer in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/innovation_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/innovation_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/innovation_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/innovation_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_innovation.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_innovation in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/nemesis_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/nemesis_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/nemesis_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/nemesis_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_nemesis.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_nemesis in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/pcjss_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/pcjss_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/pcjss_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/pcjss_sounds.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_pcjss.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_pcjss in data file mounter 0000000141a43eb0.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/ruffian_game.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/ruffian_game.dat in data file mounter 0000000141a42838.
[     78781] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/ruffian_sounds.dat.
[     78781] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/ruffian_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_ruffian.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_ruffian in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/sanchez_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/sanchez_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/sanchez_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/sanchez_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_sanchez.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_sanchez in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/sovereign_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/sovereign_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/sovereign_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/sovereign_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_sovereign.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_sovereign in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/thrust_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/thrust_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/thrust_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/thrust_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_thrust.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_thrust in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/vader_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/vader_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/vader_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/vader_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_vader.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_vader in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/motorcycles/audioconfig/vindicator_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/vindicator_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/motorcycles/audioconfig/vindicator_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/audioconfig/vindicator_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/motorcycles/sfx/dlc_vindicator.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/motorcycles/sfx/dlc_vindicator in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/blade_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/blade_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/blade_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/blade_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_blade.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_blade in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/buccaneer_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/buccaneer_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/buccaneer_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/buccaneer_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_buccaneer.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_buccaneer in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/chino_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/chino_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/chino_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/chino_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_chino.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_chino in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/coquette3_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/coquette3_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/coquette3_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/coquette3_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_coquette3.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_coquette3 in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/dominator_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/dominator_game.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/dominator_sounds.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/dominator_sounds.dat in data file mounter 0000000141a42838.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_dominator.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_dominator in data file mounter 0000000141a43eb0.
[     78797] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/dominator2_game.dat.
[     78797] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/dominator2_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/dominator2_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/dominator2_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_dominator2.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_dominator2 in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/dukes_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/dukes_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/dukes_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/dukes_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_dukes.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_dukes in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/gauntlet_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/gauntlet_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/gauntlet_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/gauntlet_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_gauntlet.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_gauntlet in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/gauntlet2_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/gauntlet2_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/gauntlet2_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/gauntlet2_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_gauntlet2.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_gauntlet2 in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/hotknife_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/hotknife_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/hotknife_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/hotknife_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_hotknife.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_hotknife in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/phoenix_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/phoenix_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/phoenix_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/phoenix_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_phoenix.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_phoenix in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/picador_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/picador_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/picador_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/picador_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_picador.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_picador in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/rloader_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/rloader_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/rloader_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/rloader_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_rloader.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_rloader in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/rloader2_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/rloader2_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/rloader2_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/rloader2_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_rloader2.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_rloader2 in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/ruiner_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/ruiner_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/ruiner_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/ruiner_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_ruiner.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_ruiner in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/sabregt_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/sabregt_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/sabregt_sounds.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/sabregt_sounds.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_sabregt.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_sabregt in data file mounter 0000000141a43eb0.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/slamvan_game.dat.
[     78812] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/slamvan_game.dat in data file mounter 0000000141a42838.
[     78812] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/slamvan_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/slamvan_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_slamvan.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_slamvan in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/stallion_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/stallion_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/stallion_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/stallion_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_stallion.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_stallion in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/stallion2_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/stallion2_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/stallion2_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/stallion2_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_stallion2.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_stallion2 in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/vigero_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/vigero_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/vigero_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/vigero_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_vigero.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_vigero in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/virgo_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/virgo_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/virgo_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/virgo_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_virgo.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_virgo in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/muscle/audioconfig/voodoo2_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/voodoo2_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/muscle/audioconfig/voodoo2_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/audioconfig/voodoo2_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/muscle/sfx/dlc_voodoo2.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/muscle/sfx/dlc_voodoo2 in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/npolchar/audioconfig/npolchar_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/npolchar/audioconfig/npolchar_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/npolchar/audioconfig/npolchar_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/npolchar/audioconfig/npolchar_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/npolchar/sfx/dlc_npolchar.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/npolchar/sfx/dlc_npolchar in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/bifta_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/bifta_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/bifta_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/bifta_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_bifta.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_bifta in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/blazer_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/blazer_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/blazer_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/blazer_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_blazer.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_blazer in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/bodhi_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/bodhi_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/bodhi_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/bodhi_sounds.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_bodhi.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_bodhi in data file mounter 0000000141a43eb0.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/brawler_game.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/brawler_game.dat in data file mounter 0000000141a42838.
[     78828] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/brawler_sounds.dat.
[     78828] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/brawler_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_brawler.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_brawler in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/dloader_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/dloader_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/dloader_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/dloader_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_dloader.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_dloader in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/dubsta3is_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/dubsta3is_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/dubsta3is_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/dubsta3is_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_dubsta3is.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_dubsta3is in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/dune_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/dune_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/dune_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/dune_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_dune.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_dune in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/injection_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/injection_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/injection_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/injection_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_injection.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_injection in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/kalahari_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/kalahari_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/kalahari_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/kalahari_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_kalahari.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_kalahari in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/marshall_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/marshall_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/marshall_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/marshall_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_marshall.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_marshall in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/mesa3_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/mesa3_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/mesa3_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/mesa3_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_mesa3.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_mesa3 in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/monster_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/monster_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/monster_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/monster_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_monster.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_monster in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/rancher_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/rancher_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/rancher_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/rancher_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_rancher.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_rancher in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/rebel_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/rebel_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/rebel_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/rebel_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_rebel.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_rebel in data file mounter 0000000141a43eb0.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/offroad/audioconfig/sandking_game.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/sandking_game.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/offroad/audioconfig/sandking_sounds.dat.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/audioconfig/sandking_sounds.dat in data file mounter 0000000141a42838.
[     78844] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/offroad/sfx/dlc_sandking.
[     78844] [FiveM_GTAProce]             MainThrd/ done loading resources:/offroad/sfx/dlc_sandking in data file mounter 0000000141a43eb0.
[     78859] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/rhinesed/data/carcols.meta.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/rhinesed/data/carcols.meta in data file mounter 0000000141a7aa28.
[     78859] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/rhinesed/data/vehicles.meta.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/rhinesed/data/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78859] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/rhinesed/data/carvariations.meta.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/rhinesed/data/carvariations.meta in data file mounter 0000000141a7aa10.
[     78859] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/rt3000varis/data/carcols.meta.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/rt3000varis/data/carcols.meta in data file mounter 0000000141a7aa28.
[     78859] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/rt3000varis/data/vehicles.meta.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/rt3000varis/data/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78859] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/rt3000varis/data/carvariations.meta.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/rt3000varis/data/carvariations.meta in data file mounter 0000000141a7aa10.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/asea_game.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/asea_game.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/asea_sounds.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/asea_sounds.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_asea.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_asea in data file mounter 0000000141a43eb0.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/asterope_game.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/asterope_game.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/asterope_sounds.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/asterope_sounds.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_asterope.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_asterope in data file mounter 0000000141a43eb0.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/emperor_game.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/emperor_game.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/emperor_sounds.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/emperor_sounds.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_emperor.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_emperor in data file mounter 0000000141a43eb0.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/fugitive_game.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/fugitive_game.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/fugitive_sounds.dat.
[     78859] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/fugitive_sounds.dat in data file mounter 0000000141a42838.
[     78859] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_fugitive.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_fugitive in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/glendale_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/glendale_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/glendale_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/glendale_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_glendale.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_glendale in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/ingot_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/ingot_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/ingot_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/ingot_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_ingot.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_ingot in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/intruder_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/intruder_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/intruder_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/intruder_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_intruder.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_intruder in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/premier_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/premier_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/premier_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/premier_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_premier.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_premier in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/primo_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/primo_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/primo_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/primo_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_primo.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_primo in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/regina_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/regina_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/regina_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/regina_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_regina.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_regina in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/schafter2_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/schafter2_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/schafter2_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/schafter2_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_schafter2.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_schafter2 in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/stainer_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/stainer_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/stainer_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/stainer_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_stainer.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_stainer in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/stratum_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/stratum_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/stratum_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/stratum_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_stratum.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_stratum in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/superd_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/superd_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/superd_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/superd_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_superd.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_superd in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/tailgate_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/tailgate_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/tailgate_sounds.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/tailgate_sounds.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_tailgate.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_tailgate in data file mounter 0000000141a43eb0.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/warrener_game.dat.
[     78875] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/warrener_game.dat in data file mounter 0000000141a42838.
[     78875] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/warrener_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/warrener_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_warrener.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_warrener in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sedans/audioconfig/washington_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/washington_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sedans/audioconfig/washington_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/audioconfig/washington_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sedans/sfx/dlc_washington.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sedans/sfx/dlc_washington in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_FILE resources:/sent5pd/data/carcols.meta.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sent5pd/data/carcols.meta in data file mounter 0000000141a7aa28.
[     78891] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/sent5pd/data/vehicles.meta.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sent5pd/data/vehicles.meta in data file mounter 0000000141a7a9f8.
[     78891] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/sent5pd/data/carvariations.meta.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sent5pd/data/carvariations.meta in data file mounter 0000000141a7aa10.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/service/audioconfig/taxi_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/service/audioconfig/taxi_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/service/audioconfig/taxi_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/service/audioconfig/taxi_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/service/sfx/dlc_taxi.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/service/sfx/dlc_taxi in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/service/audioconfig/phantom_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/service/audioconfig/phantom_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/service/audioconfig/phantom_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/service/audioconfig/phantom_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/service/sfx/dlc_phantom.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/service/sfx/dlc_phantom in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/ninef2_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/ninef2_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/ninef2_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/ninef2_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_ninef2.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_ninef2 in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/alpha_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/alpha_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/alpha_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/alpha_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_alpha.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_alpha in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/banshee_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/banshee_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/banshee_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/banshee_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_banshee.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_banshee in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/blista2_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/blista2_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/blista2_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/blista2_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_blista2.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_blista2 in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/buffalo_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/buffalo_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/buffalo_sounds.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/buffalo_sounds.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_buffalo.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_buffalo in data file mounter 0000000141a43eb0.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/buffalo2_game.dat.
[     78891] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/buffalo2_game.dat in data file mounter 0000000141a42838.
[     78891] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/buffalo2_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/buffalo2_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_buffalo2.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_buffalo2 in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/buffalo3_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/buffalo3_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/buffalo3_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/buffalo3_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_buffalo3.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_buffalo3 in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/carbonizzare_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/carbonizzare_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/carbonizzare_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/carbonizzare_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_carbonizzare.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_carbonizzare in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/comet_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/comet_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/comet_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/comet_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_comet.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_comet in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/coquette_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/coquette_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/coquette_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/coquette_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_coquette.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_coquette in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/elegy2_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/elegy2_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/elegy2_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/elegy2_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_elegy2.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_elegy2 in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/fusilade_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/fusilade_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/fusilade_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/fusilade_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_fusilade.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_fusilade in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/jester_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/jester_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/jester_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/jester_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_jester.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_jester in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/jester2_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/jester2_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/jester2_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/jester2_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_jester2.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_jester2 in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/massacro_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/massacro_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/massacro_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/massacro_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_massacro.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_massacro in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/massacro2_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/massacro2_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/massacro2_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/massacro2_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_massacro2.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_massacro2 in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/penumbra_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/penumbra_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/penumbra_sounds.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/penumbra_sounds.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_penumbra.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_penumbra in data file mounter 0000000141a43eb0.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/rapidgt_game.dat.
[     78906] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/rapidgt_game.dat in data file mounter 0000000141a42838.
[     78906] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/rapidgt_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/rapidgt_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_rapidgt.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_rapidgt in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/schwarzer_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/schwarzer_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/schwarzer_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/schwarzer_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_schwarzer.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_schwarzer in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/sultan_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/sultan_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/sultan_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/sultan_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_sultan.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_sultan in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/surano_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/surano_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/surano_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/surano_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_surano.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_surano in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/np02asiani6_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np02asiani6_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/np02asiani6_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np02asiani6_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_np02asiani6.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_np02asiani6 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/np03asianf4_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np03asianf4_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/np03asianf4_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np03asianf4_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_np03asianf4.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_np03asianf4 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/np04eurov8_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np04eurov8_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/np04eurov8_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np04eurov8_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_np04eurov8.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_np04eurov8 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/np07streetasiani6_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np07streetasiani6_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/np07streetasiani6_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np07streetasiani6_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_np07streetasiani6.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_np07streetasiani6 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sports/audioconfig/np08raceeurof6_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np08raceeurof6_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sports/audioconfig/np08raceeurof6_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/audioconfig/np08raceeurof6_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sports/sfx/dlc_np08raceeurof6.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sports/sfx/dlc_np08raceeurof6 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/coquette2_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/coquette2_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/coquette2_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/coquette2_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_coquette2.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_coquette2 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/feltzer3_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/feltzer3_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/feltzer3_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/feltzer3_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_feltzer3.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_feltzer3 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/jb700_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/jb700_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/jb700_sounds.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/jb700_sounds.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_jb700.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_jb700 in data file mounter 0000000141a43eb0.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/manana_game.dat.
[     78922] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/manana_game.dat in data file mounter 0000000141a42838.
[     78922] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/manana_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/manana_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_manana.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_manana in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/monroe_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/monroe_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/monroe_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/monroe_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_monroe.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_monroe in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/peyote_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/peyote_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/peyote_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/peyote_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_peyote.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_peyote in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/pigalle_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/pigalle_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/pigalle_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/pigalle_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_pigalle.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_pigalle in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/roosevelt_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/roosevelt_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/roosevelt_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/roosevelt_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_roosevelt.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_roosevelt in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/stinger_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/stinger_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/stinger_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/stinger_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_stinger.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_stinger in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/tornado_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/tornado_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/tornado_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/tornado_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_tornado.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_tornado in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/sportsclassic/audioconfig/ztype_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/ztype_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/sportsclassic/audioconfig/ztype_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/audioconfig/ztype_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/sportsclassic/sfx/dlc_ztype.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/sportsclassic/sfx/dlc_ztype in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/adder_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/adder_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/adder_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/adder_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_adder.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_adder in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/bullet_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/bullet_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/bullet_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/bullet_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_bullet.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_bullet in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/cheetah_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/cheetah_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/cheetah_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/cheetah_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_cheetah.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_cheetah in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/entityxf_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/entityxf_game.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/entityxf_sounds.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/entityxf_sounds.dat in data file mounter 0000000141a42838.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_entityxf.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_entityxf in data file mounter 0000000141a43eb0.
[     78937] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/infernus_game.dat.
[     78937] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/infernus_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/infernus_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/infernus_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_infernus.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_infernus in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/osiris_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/osiris_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/osiris_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/osiris_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_osiris.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_osiris in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/sut20_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/sut20_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/sut20_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/sut20_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_sut20.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_sut20 in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/turismor_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/turismor_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/turismor_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/turismor_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_turismor.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_turismor in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/np01eurov10_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/np01eurov10_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/np01eurov10_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/np01eurov10_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_np01eurov10.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_np01eurov10 in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/np05itasuperv8_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/np05itasuperv8_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/np05itasuperv8_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/np05itasuperv8_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_np05itasuperv8.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_np05itasuperv8 in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/super/audioconfig/zentorno_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/zentorno_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/super/audioconfig/zentorno_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/audioconfig/zentorno_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/super/sfx/dlc_zentorno.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/super/sfx/dlc_zentorno in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/baller_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/baller_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/baller_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/baller_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_baller.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_baller in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/baller2_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/baller2_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/baller2_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/baller2_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_baller2.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_baller2 in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/bjxl_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/bjxl_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/bjxl_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/bjxl_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_bjxl.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_bjxl in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/cavalcade_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/cavalcade_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/cavalcade_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/cavalcade_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_cavalcade.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_cavalcade in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/cavalcade2_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/cavalcade2_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/cavalcade2_sounds.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/cavalcade2_sounds.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_cavalcade2.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_cavalcade2 in data file mounter 0000000141a43eb0.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/dubsta_game.dat.
[     78953] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/dubsta_game.dat in data file mounter 0000000141a42838.
[     78953] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/dubsta_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/dubsta_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_dubsta.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_dubsta in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/dubsta2_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/dubsta2_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/dubsta2_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/dubsta2_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_dubsta2.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_dubsta2 in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/ifnq2_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/ifnq2_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/ifnq2_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/ifnq2_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_ifnq2.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_ifnq2 in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/granger_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/granger_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/granger_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/granger_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_granger.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_granger in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/gresley_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/gresley_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/gresley_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/gresley_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_gresley.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_gresley in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/habanero_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/habanero_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/habanero_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/habanero_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_habanero.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_habanero in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/huntley_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/huntley_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/huntley_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/huntley_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_huntley.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_huntley in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/landstalker_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/landstalker_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/landstalker_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/landstalker_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_landstalker.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_landstalker in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/mesa_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/mesa_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/mesa_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/mesa_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_mesa.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_mesa in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/patriot_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/patriot_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/patriot_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/patriot_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_patriot.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_patriot in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/radi_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/radi_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/radi_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/radi_sounds.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_radi.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_radi in data file mounter 0000000141a43eb0.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/rocoto_game.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/rocoto_game.dat in data file mounter 0000000141a42838.
[     78969] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/rocoto_sounds.dat.
[     78969] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/rocoto_sounds.dat in data file mounter 0000000141a42838.
[     78984] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_rocoto.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_rocoto in data file mounter 0000000141a43eb0.
[     78984] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/seminole_game.dat.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/seminole_game.dat in data file mounter 0000000141a42838.
[     78984] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/seminole_sounds.dat.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/seminole_sounds.dat in data file mounter 0000000141a42838.
[     78984] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_seminole.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_seminole in data file mounter 0000000141a43eb0.
[     78984] [FiveM_GTAProce]             MainThrd/ loading AUDIO_GAMEDATA resources:/suvs/audioconfig/serrano_game.dat.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/serrano_game.dat in data file mounter 0000000141a42838.
[     78984] [FiveM_GTAProce]             MainThrd/ loading AUDIO_SOUNDDATA resources:/suvs/audioconfig/serrano_sounds.dat.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/audioconfig/serrano_sounds.dat in data file mounter 0000000141a42838.
[     78984] [FiveM_GTAProce]             MainThrd/ loading AUDIO_WAVEPACK resources:/suvs/sfx/dlc_serrano.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/suvs/sfx/dlc_serrano in data file mounter 0000000141a43eb0.
[     78984] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/lation_weed/stream/shoe_shuffler_weed.ytyp.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/lation_weed/stream/shoe_shuffler_weed.ytyp in data file mounter 00007ffd97b12c58.
[     78984] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/okokPhone_props/okok_phone_b.ytyp.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/okokPhone_props/okok_phone_b.ytyp in data file mounter 00007ffd97b12c58.
[     78984] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rcore_prison_assets/stream/rcore_assets.ytyp.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/rcore_prison_assets/stream/rcore_assets.ytyp in data file mounter 00007ffd97b12c58.
[     78984] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rcore_prison_assets/stream/v_fences.ytyp.
[     78984] [FiveM_GTAProce]             MainThrd/ Removing existing #typ v_fences
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/rcore_prison_assets/stream/v_fences.ytyp in data file mounter 00007ffd97b12c58.
[     78984] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rcore_prison_assets/stream/h4_prop_h4_accs_01.ytyp.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/rcore_prison_assets/stream/h4_prop_h4_accs_01.ytyp in data file mounter 00007ffd97b12c58.
[     78984] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rcore_prison_assets/stream/ch_prop_ch_collectibles_freemode.ytyp.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/rcore_prison_assets/stream/ch_prop_ch_collectibles_freemode.ytyp in data file mounter 00007ffd97b12c58.
[     78984] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_ENTRY RELOAD_MAP_STORE.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading RELOAD_MAP_STORE in data file mounter 00007ffd97b12af8.
[     78984] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_CACHE rcore_prison_assets.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading rcore_prison_assets in data file mounter 00007ffd97b12b30.
[     78984] [FiveM_GTAProce]             MainThrd/ loading CARCOLS_GEN9_FILE resources:/rcore_tuning/data/carcols_gen9.meta.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/rcore_tuning/data/carcols_gen9.meta in data file mounter 0000000141ab9e70.
[     78984] [FiveM_GTAProce]             MainThrd/ loading CARMODCOLS_GEN9_FILE resources:/rcore_tuning/data/carmodcols_gen9.meta.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/rcore_tuning/data/carmodcols_gen9.meta in data file mounter 0000000141ab9e70.
[     78984] [FiveM_GTAProce]             MainThrd/ loading FIVEM_LOVES_YOU_447B37BE29496FA0 resources:/rcore_tuning/data/carmodcols.ymt.
[     78984] [FiveM_GTAProce]             MainThrd/ done loading resources:/rcore_tuning/data/carmodcols.ymt in data file mounter 0000000141ab9e70.
[     78984] [FiveM_GTAProce]             MainThrd/ loading CONDITIONAL_ANIMS_FILE resources:/rpemotes-reborn/conditionalanims.meta.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/conditionalanims.meta in data file mounter 0000000141a3d3e8.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/rpemotesreborn_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/rpemotesreborn_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/brummie_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/brummie_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/bzzz_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/bzzz_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/bzzz_camp_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/bzzz_camp_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/apple_1.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/apple_1.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/kaykaymods_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/kaykaymods_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/knjgh_pizzas.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/knjgh_pizzas.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/natty_props_lollipops.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/natty_props_lollipops.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/ultra_ringcase.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/ultra_ringcase.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/pata_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/pata_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/vedere_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/vedere_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/pnwsigns.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/pnwsigns.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/pprp_icefishing.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/pprp_icefishing.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/scully_props.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/scully_props.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/samnick_prop_lighter01.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/samnick_prop_lighter01.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/rpemotes-reborn/stream/bzzz_murderpack.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/rpemotes-reborn/stream/bzzz_murderpack.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/wasabi_ambulance/stream/wasabi_stretcher.ytyp.
[     79000] [FiveM_GTAProce]             MainThrd/ done loading resources:/wasabi_ambulance/stream/wasabi_stretcher.ytyp in data file mounter 00007ffd97b12c58.
[     79000] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/wasabi_crutch/stream/crutch.ytyp.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading resources:/wasabi_crutch/stream/crutch.ytyp in data file mounter 00007ffd97b12c58.
[     79016] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_METADATA_FILE resources:/wasabi_crutch/chair_data/vehicles.meta.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading resources:/wasabi_crutch/chair_data/vehicles.meta in data file mounter 0000000141a7a9f8.
[     79016] [FiveM_GTAProce]             MainThrd/ loading VEHICLE_VARIATION_FILE resources:/wasabi_crutch/chair_data/carvariations.meta.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading resources:/wasabi_crutch/chair_data/carvariations.meta in data file mounter 0000000141a7aa10.
[     79016] [FiveM_GTAProce]             MainThrd/ loading DLC_ITYP_REQUEST resources:/wasabi_police/stream/tag.ytyp.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading resources:/wasabi_police/stream/tag.ytyp in data file mounter 00007ffd97b12c58.
[     79016] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_ENTRY RELOAD_MAP_STORE.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading RELOAD_MAP_STORE in data file mounter 00007ffd97b12af8.
[     79016] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_CACHE TheMightCafe.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading TheMightCafe in data file mounter 00007ffd97b12b30.
[     79016] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_ENTRY RELOAD_MAP_STORE.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading RELOAD_MAP_STORE in data file mounter 00007ffd97b12af8.
[     79016] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_CACHE dnxnewcalafiaroads.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading dnxnewcalafiaroads in data file mounter 00007ffd97b12b30.
[     79016] [FiveM_GTAProce]             MainThrd/ loading CFX_PSEUDO_ENTRY RELOAD_MAP_STORE.
[     79016] [FiveM_GTAProce]             MainThrd/ done loading RELOAD_MAP_STORE in data file mounter 00007ffd97b12af8.
[     79016] [FiveM_GTAProce]             MainThrd/ Performing deferred RELOAD_MAP_STORE.
[     79016] [FiveM_GTAProce]             MainThrd/ Loaded bh1_14_0.ybn (id 1391)
[     79016] [FiveM_GTAProce]             MainThrd/ Loaded bt1_01_0.ybn (id 931)
[     79016] [FiveM_GTAProce]             MainThrd/ Loaded cs4_13_0.ybn (id 5527)
[     79016] [FiveM_GTAProce]             MainThrd/ Loaded cs4_13_21.ybn (id 5541)
[     79031] [FiveM_GTAProce]             MainThrd/ Duplicate Archetype 'bzzz_camp_food_marshmallow' (DE47BB3D), seen in 'bzzz_camp_props.ytyp' and 'bzzz_camp_props.ytyp'
[     79031] [FiveM_GTAProce]             MainThrd/ Duplicate Archetype 'bzzz_camp_stick_marshmallow' (C9FF7B7A), seen in 'bzzz_camp_props.ytyp' and 'bzzz_camp_props.ytyp'
[     79031] [FiveM_GTAProce]             MainThrd/ Loaded cs4_13_5.ybn (id 5545)
[     79031] [FiveM_GTAProce]             MainThrd/ Loaded cs4_13_6.ybn (id 5546)
[     79031] [FiveM_GTAProce]             MainThrd/ Loaded cs4_13_7.ybn (id 5547)
[     79031] [FiveM_GTAProce]             MainThrd/ Loaded cs4_13_8.ybn (id 5548)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_13_9.ybn (id 5549)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_0.ybn (id 5559)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_11.ybn (id 5562)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_12.ybn (id 5563)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_13.ybn (id 5564)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_14.ybn (id 5565)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_15.ybn (id 5566)
[     79047] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_4.ybn (id 5569)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_5.ybn (id 5570)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded cs4_14_9.ybn (id 5574)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad01.ybn (id 14420)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad02.ybn (id 14421)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad03.ybn (id 14422)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad_armco01.ybn (id 14423)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad_armco02.ybn (id 14424)
[     79062] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad_bridge01.ybn (id 14425)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad_bridge02.ybn (id 14426)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_calafiaroad_bridge03.ybn (id 14427)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_newcalafiabridge.ybn (id 14428)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_newcalafiabridge_lighting.ybn (id 14429)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway01.ybn (id 14430)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway02.ybn (id 14431)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway03.ybn (id 14432)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway04.ybn (id 14433)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway05.ybn (id 14434)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway_armco01.ybn (id 14435)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway_armco02.ybn (id 14436)
[     79078] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway_armco03.ybn (id 14437)
[     79094] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway_armco04.ybn (id 14438)
[     79094] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway_armco05.ybn (id 14439)
[     79094] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newcalafiaroads_northcalafiaway_bridge01.ybn (id 14440)
[     79094] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newsenoraroad_armco01a.ybn (id 14441)
[     79109] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newsenoraroad_armco01b.ybn (id 14442)
[     79109] [FiveM_GTAProce]             MainThrd/ Loaded dnx_newsenoraroad_road01.ybn (id 14443)
[     79109] [FiveM_GTAProce]             MainThrd/ Loaded hei_ap1_03_4.ybn (id 10089)
[     79109] [FiveM_GTAProce]             MainThrd/ Loaded hei_bh1_20_0.ybn (id 10136)
[     79125] [FiveM_GTAProce]             MainThrd/ Loaded hei_ch3_08_17.ybn (id 11149)
[     79125] [FiveM_GTAProce]             MainThrd/ Loaded hei_ch3_12_16.ybn (id 11237)
[     79125] [FiveM_GTAProce]             MainThrd/ Loaded hei_ch3_12_17.ybn (id 11238)
[     79125] [FiveM_GTAProce]             MainThrd/ Loaded hei_vb_34_2.ybn (id 10330)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded hi@bh1_33_0.ybn (id 1435)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded hi@bt1_01_0.ybn (id 932)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded hi@cs4_13_0.ybn (id 5550)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded hi@cs4_14_0.ybn (id 5575)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded hi@cs4_14_11.ybn (id 5578)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded id2_18_1.ybn (id 830)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded lr_cs4_04_0.ybn (id 12285)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded lr_cs4_10_3.ybn (id 12281)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded m24_2_garment_factory_1.ybn (id 14419)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded po1_05_4.ybn (id 1218)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded remiix_cafe_col.ybn (id 14418)
[     79141] [FiveM_GTAProce]             MainThrd/ Loaded v_hospital.ybn (id 8547)
[     79156] [FiveM_GTAProce]             MainThrd/ Loaded vb_20_0.ybn (id 1875)
[     79156] [FiveM_GTAProce]             MainThrd/ Loaded vb_34_2.ybn (id 1935)
[     79172] [FiveM_GTAProce]             MainThrd/ rage::gameSkeleton::RunInitFunctions: Done running INIT_SESSION init functions!
[     79719] [FiveM_GTAProce]             MainThrd/ ^2Game finished loading!
[     79766] [FiveM_GTAProce]             MainThrd/ HostState transitioning from HS_IDLE to HS_LOADED
[     79797] [FiveM_GTAProce]             MainThrd/ HostState transitioning from HS_LOADED to HS_START_HOSTING
[     79922] [FiveM_GTAProce]             MainThrd/ HostState transitioning from HS_START_HOSTING to HS_HOSTING
[     79953] [FiveM_GTAProce]             MainThrd/ HostState transitioning from HS_HOSTING to HS_HOSTING_NET_GAME
[     80672] [FiveM_GTAProce]             MainThrd/ HostState transitioning from HS_HOSTING_NET_GAME to HS_HOSTED
[     80687] [FiveM_GTAProce]             MainThrd/ Creating script environments for monitor
[     80703] [FiveM_GTAProce]             MainThrd/ Creating script environments for mapmanager
[     80703] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80719] [FiveM_GTAProce]             MainThrd/ Creating script environments for chat
[     80734] [FiveM_GTAProce]             MainThrd/ Creating script environments for spawnmanager
[     80734] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80750] [FiveM_GTAProce]             MainThrd/ Creating script environments for sessionmanager
[     80750] [FiveM_GTAProce]             MainThrd/ Creating script environments for hardcap
[     80766] [FiveM_GTAProce]             MainThrd/ Creating script environments for baseevents
[     80766] [FiveM_GTAProce]             MainThrd/ Creating script environments for ox_lib
[     80766] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80781] [FiveM_GTAProce]             MainThrd/ Creating script environments for oxmysql
[     80781] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_core
[     80797] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80812] [FiveM_GTAProce]             MainThrd/ Creating script environments for okokChatV2
[     80828] [FiveM_GTAProce]             MainThrd/ Creating script environments for ox_target
[     80828] [FiveM_GTAProce]             MainThrd/ Creating script environments for ox_doorlock
[     80828] [FiveM_GTAProce]             MainThrd/ Creating script environments for ox_inventory
[     80828] [FiveM_GTAProce]        CrBrowserMain/ Autofocus processing was blocked because a document already has a focused element. (@game/ui/root.html:0)
[     80844] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80859] [FiveM_GTAProce]             MainThrd/ Creating script environments for ox_fuel
[     80859] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_adminmenu
[     80859] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80891] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_binoculars
[     80891] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_busjob
[     80891] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_carwash
[     80891] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_cityhall
[     80906] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80922] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_divegear
[     80922] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_diving
[     80922] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_drugs
[     80937] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_fireworks
[     80937] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80953] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_garbagejob
[     80953] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_houserobbery
[     80969] [FiveM_GTAProce]             MainThrd/ Creating script environments for MugShotBase64
[     80969] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     80984] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_idcard
[     80984] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_lapraces
[     81000] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_management
[     81000] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_medical
[     81000] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81016] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_newsjob
[     81016] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_properties
[     81031] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_radialmenu
[     81031] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81047] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_recyclejob
[     81062] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_scoreboard
[     81062] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_seatbelt
[     81062] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_smallresources
[     81062] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81078] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_streetraces
[     81094] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_taxijob
[     81094] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_towjob
[     81094] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_truckerjob
[     81109] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81125] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_vehiclesales
[     81125] [FiveM_GTAProce]             MainThrd/ Creating script environments for qbx_vineyard
[     81125] [FiveM_GTAProce]             MainThrd/ Creating script environments for bob74_ipl
[     81141] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81156] [FiveM_GTAProce]             MainThrd/ Creating script environments for illenium-appearance
[     81172] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81187] [FiveM_GTAProce]             MainThrd/ Creating script environments for mana_audio
[     81187] [FiveM_GTAProce]             MainThrd/ Creating script environments for mhacking
[     81187] [FiveM_GTAProce]             MainThrd/ Creating script environments for safecracker
[     81219] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81219] [FiveM_GTAProce]             MainThrd/ Creating script environments for screencapture
[     81281] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81281] [FiveM_GTAProce]             MainThrd/ Creating script environments for screenshot-basic
[     81312] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81328] [FiveM_GTAProce]             MainThrd/ Creating script environments for ultra-voltlab
[     81328] [FiveM_GTAProce]             MainThrd/ Creating script environments for vehiclehandler
[     81328] [FiveM_GTAProce]             MainThrd/ Creating script environments for pma-voice
[     81344] [FiveM_GTAProce]             MainThrd/ Still executing script initialization routines...
[     81359] [FiveM_GTAProce]             MainThrd/ Creating script environments for mm_radio
[     81359] [FiveM_GTAProce]             MainThrd/ Creating script environments for pillbox
[     81359] [FiveM_GTAProce]             MainThrd/ Creating script environments for lation_selling
[     81391] [FiveM_GTAProce]        CrBrowserMain/ THREE.WebGLRenderer 101dev (@screenshot-basic/dist/ui.html:15)
[     81437] [FiveM_GTAProce]        CrBrowserMain/ f? (@screenshot-basic/dist/ui.html:15)
[     83172] [FiveM_GTAProce]        CrBrowserMain/ Uncaught (in promise) TypeError: Failed to fetch (@valic_loading/web/game.js:64)
[     96328] [FiveM_GTAProce]        CrBrowserMain/ Request Failed due to timeout: https://illenium-appearance/appearance_get_locales (@illenium-appearance/web/dist/assets/index.b8e72b46.js:70)
[     96328] [FiveM_GTAProce]        CrBrowserMain/ Request Failed due to timeout: https://illenium-appearance/get_theme_configuration (@illenium-appearance/web/dist/assets/index.b8e72b46.js:70)
[    111328] [FiveM_GTAProce]        CrBrowserMain/ Request Failed due to timeout: https://illenium-appearance/appearance_get_locales (@illenium-appearance/web/dist/assets/index.b8e72b46.js:70)
[    111328] [FiveM_GTAProce]        CrBrowserMain/ Request Failed due to timeout: https://illenium-appearance/get_theme_configuration (@illenium-appearance/web/dist/assets/index.b8e72b46.js:70)
[    118531] [FiveM_DumpServ]                54680/ Process crash captured. Crash dialog content:
[    118531] [FiveM_DumpServ]                54680/ citizen-scripting-lua54.dll+2E4FCA
[    118531] [FiveM_DumpServ]                54680/ An error at citizen-scripting-lua54.dll+2E4FCA caused FiveM to stop working. A crash report is being uploaded to the FiveM developers.
[    118531] [FiveM_DumpServ]                54680/ Stack trace:
[    118531] [FiveM_DumpServ]                54680/   citizen-scripting-lua54.dll+2E4FCA
[    118531] [FiveM_DumpServ]                54680/   citizen-scripting-lua54.dll+2E6F08
[    118531] [FiveM_DumpServ]                54680/   citizen-scripting-lua54.dll+2E6B09
[    118531] [FiveM_DumpServ]                54680/   citizen-scripting-lua54.dll+2FDE4A
[    118531] [FiveM_DumpServ]                54680/   citizen-scripting-lua54.dll+2FA963
[    118531] [FiveM_DumpServ]                54680/   citizen-scripting-lua54.dll+2F98F3
[    118531] [FiveM_DumpServ]                54680/   citizen-scripting-lua54.dll+2FAE90
[    118531] [FiveM_DumpServ]                54680/ 
[    119359] [FiveM_DumpServ]                54680/ Crash report service returned si-690d6456b519433e98b855af8561a422
